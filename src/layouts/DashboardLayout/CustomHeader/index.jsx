import React, { useState } from "react";
import {
  CaretDownOutlined,
  LockOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Avatar, Button, Dropdown, Layout, Modal, theme } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useMediaQuery } from "react-responsive";

import { logout } from "../../../store/auth";
import Api from "../../../utils/apiHandler";
import { ApiRoute } from "../../../config/apiEndpoint";
import { browserRoutes } from "../../../config/browserRoutes";
import { showToast } from "../../../utils/showToast";
import { showError } from "../../../utils/showError";
import { themeConfig } from "../../../config/theme.config";
import Logo from "../../../assets/images/Logo.png";
import AdminLogo from "../../../assets/images/Logo.png";
const { Header } = Layout;

const CustomHeader = ({ setCollapsed, collapsed }) => {
  const _767 = useMediaQuery({ maxWidth: 767 });
  const authData = useSelector((state) => state?.auth);
  const [openLogoutConfirmModal, setOpenLogoutConfirmModal] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      // const res = await Api("DELETE", ApiRoute?.logout);
      showToast.success('Logout successfully');
      dispatch(logout());
      // if (res?.data?.status) {
      //   setOpenLogoutConfirmModal(false);
      //   showToast.success(res?.data?.message);
      //   dispatch(logout());
      // } else {
      //   showToast.error(res?.data?.message);
      // }
      setLogoutLoading(false);
    } catch (error) {
      setLogoutLoading(false);
      showError(error);
    }
  };
  const userDropdownItems = [
    {
      label: (
        <span
          className="flex items-center gap-2"
          onClick={() => navigate(browserRoutes?.profile)}
        >
          <UserOutlined />
          Profile
        </span>
      ),
      key: "0",
    },
    {
      label: (
        <span
          className="flex items-center gap-2"
          onClick={() => navigate(browserRoutes?.changePassword)}
        >
          <LockOutlined />
          Change Password
        </span>
      ),
      key: "1",
    },
    {
      label: (
        <span
          className="flex items-center gap-2"
          onClick={() => setOpenLogoutConfirmModal(true)}
        >
          <LogoutOutlined />
          Logout
        </span>
      ),
      key: "2",
    },
  ];
  return (
    <>
      <Header
        className="flex items-center justify-between px-4 border-0 border-b border-solid border-b-[rgba(1,1,1,0.1)]"
        style={{
          background: colorBgContainer,
        }}
      >
        <div className="flex items-center gap-3">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed((prev) => !prev)}
          />
          {_767 && (
            <div
              className="flex items-center gap-3 cursor-pointer"
              onClick={() => navigate(browserRoutes?.analytics)}
            >
              <img src={Logo} alt="logo" className=" h-[45px]" />
            </div>
          )}
        </div>
        <Dropdown
          menu={{
            items: userDropdownItems,
          }}
          trigger={["click"]}
        >
          <div className="flex items-center gap-2 cursor-pointer">
            {authData?.profileImage ? (
              <Avatar
                size={_767 ? "default" : "large"}
                src={<img src={authData?.profileImage} alt="user-avatar" />}
              />
            ) : (
              <Avatar
                size={_767 ? "default" : "large"}
                className="bg-primary"
                src={
                  <img
                    src={AdminLogo}
                    className="!object-contain"
                    alt="user-avatar"
                  />
                }
              // icon={<UserOutlined className="text-white text-lg" />}
              />
            )}
            {!_767 && (
              <div className="flex flex-col justify-center text-black">
                <span className="text-sm font-medium capitalize">
                  {authData?.firstName ?? ""} {authData?.lastName ?? ""}
                </span>
                <span className="text-xs">{authData?.email ?? ""}</span>
              </div>
            )}
            <CaretDownOutlined className="text-black" />
          </div>
        </Dropdown>
      </Header>
      <Modal
        title="Logout"
        open={openLogoutConfirmModal}
        onOk={handleLogout}
        onCancel={() => setOpenLogoutConfirmModal(false)}
        okText={"Logout"}
        confirmLoading={logoutLoading}
      >
        <p>Are you sure you want to logout?</p>
      </Modal>
    </>
  );
};

export default CustomHeader;

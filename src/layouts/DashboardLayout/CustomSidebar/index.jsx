import React, { useEffect } from "react";
import { Layout, Menu } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { themeConfig } from "../../../config/theme.config";
import { sidebarConfig } from "../../../config/sidebar.config";
import { browserRoutes } from "../../../config/browserRoutes";
import { StyleWrapper } from "./index.style";
import { updateSidebarSelectedKeys } from "../../../store/ui";
import Logo from "../../../assets/images/Logo.png";
const { Sider } = Layout;

const CustomSidebar = ({
  collapsed,
  collapsedWidth,
  setCollapsed,
  showCloseIcon,
  showOverlay,
  siderStyle,
  onMouseEnter,
  onMouseLeave,
  handleMenuItemClick,
}) => {
  const uiData = useSelector((state) => state.ui);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const nonSidebarRoutes = [
    browserRoutes?.profile,
    browserRoutes?.changePassword,
  ];

  useEffect(() => {
    if (nonSidebarRoutes?.includes(location?.pathname)) {
      dispatch(updateSidebarSelectedKeys([]));
    } else {
      // Find the matching sidebar route for the current path
      const currentRoute = sidebarConfig.find(item => {
        // Handle exact matches and sub-routes
        if (item.key === location?.pathname) {
          return true;
        }
        // Handle sub-routes (e.g., /category/add should match /category)
        if (location?.pathname.startsWith(item.key + '/')) {
          return true;
        }
        return false;
      });
      console.log("currentRoute.key", currentRoute.key)
      if (currentRoute) {
        dispatch(updateSidebarSelectedKeys([currentRoute.key]));
      } else if (location?.pathname === browserRoutes?.dashboard) {
        dispatch(updateSidebarSelectedKeys([browserRoutes?.dashboard]));
      }
    }
  }, [location?.pathname]);
  return (
    <StyleWrapper primarycolor={themeConfig?.primaryColor} className="h-full">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={260} //300
        style={{
          height: "100%",
          ...siderStyle,
        }}
        collapsedWidth={collapsedWidth}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        theme={themeConfig?.sidebarTheme}
        className="border-0 border-r border-solid border-r-[rgba(1,1,1,0.1)] shadow-none z-[1000]"
      >
        <div className="w-full h-full flex flex-col">
          <div
            className={`h-16 border-0 border-b border-solid border-[rgba(1,1,1,0.1)] relative flex items-center justify-center`}
          >
            <div
              className="flex justify-center items-center gap-3 cursor-pointer overflow-hidden"
              onClick={() => {
                handleMenuItemClick();
                navigate(browserRoutes?.dashboard);
              }}
            >
              <img
                src={Logo}
                alt="logo"
                className="h-[45px]"
              />
              {!collapsed && (
                <span
                  className={`text-primary text-base font-bold h-6 overflow-hidden`}
                >
                  {themeConfig?.appName}
                </span>
              )}
            </div>
          </div>
          <div className="flex-1 overflow-auto customScrollbarSm">
            <Menu
              className="pt-3 px-1"
              theme={themeConfig?.sidebarTheme}
              mode="inline"
              items={sidebarConfig}
              // selectedKeys={["/" + location?.pathname?.split("/")?.[1]]}
              selectedKeys={uiData?.sideBar?.selectedKeys}
              onClick={handleMenuItemClick}
              triggerSubMenuAction="click"
              onSelect={(data) => {
                dispatch(updateSidebarSelectedKeys([data?.key]));
              }}
            />
          </div>
        </div>
      </Sider>
      {showOverlay && !collapsed && (
        <div
          className="absolute top-0 left-0 bottom-0 right-0 bg-[rgba(1,1,1,0.2)] z-[999]"
          onClick={() => setCollapsed(true)}
        />
      )}
    </StyleWrapper>
  );
};

export default CustomSidebar;

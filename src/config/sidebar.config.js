import {
  DashboardOutlined,
  FileOutlined,
  TeamOutlined,
  BookOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
} from "@ant-design/icons";
import { Link } from "react-router-dom";

import { browserRoutes } from "./browserRoutes";

export const sidebarConfig = [
  {
    key: browserRoutes?.dashboard,
    icon: <DashboardOutlined />,
    label: <Link to={browserRoutes?.dashboard}>Dashboard</Link>,
  },
  {
    key: browserRoutes?.users,
    icon: <TeamOutlined />,
    label: <Link to={browserRoutes?.users}>Users</Link>,
  },
  {
    key: browserRoutes?.subscriptionManagement,
    icon: <DollarOutlined />,
    label: <Link to={browserRoutes?.subscriptionManagement}>Subscription Plans</Link>,
  },
  {
    key: browserRoutes?.notificationManagement,
    icon: <BellOutlined />,
    label: <Link to={browserRoutes?.notificationManagement}>Notification Management</Link>,
  },
  {
    key: browserRoutes?.category,
    icon: <span className="material-symbols-outlined">category</span>,
    label: <Link to={browserRoutes?.category}>Category</Link>,
  },
  {
    key: browserRoutes?.contactUs,
    icon: <span className="material-symbols-outlined">contact_support</span>,
    label: <Link to={browserRoutes?.contactUs}>Contact Us</Link>,
  },
  {
    key: browserRoutes?.staticPage,
    icon: <FileOutlined />,
    label: <Link to={browserRoutes?.staticPage}>Static Page</Link>,
  },
];

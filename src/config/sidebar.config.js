import {
  Bar<PERSON><PERSON>Outlined,
  FileOutlined,
  TeamOutlined,
  BookOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import { Link } from "react-router-dom";

import { browserRoutes } from "./browserRoutes";

export const sidebarConfig = [
  {
    key: browserRoutes?.analytics,
    icon: <BarChartOutlined />,
    label: <Link to={browserRoutes?.analytics}>Analytics</Link>,
  },
  {
    key: browserRoutes?.users,
    icon: <TeamOutlined />,
    label: <Link to={browserRoutes?.users}>Users</Link>,
  },
  {
    key: browserRoutes?.category,
    icon: <span className="material-symbols-outlined">category</span>,
    label: <Link to={browserRoutes?.category}>Category</Link>,
  },
  {
    key: browserRoutes?.item,
    icon: <span className="material-symbols-outlined">inventory_2</span>,
    label: <Link to={browserRoutes?.item}>Item Management</Link>,
  },
  {
    key: browserRoutes?.subscriptionManagement,
    icon: <DollarOutlined />,
    label: <Link to={browserRoutes?.subscriptionManagement}>Subscription Plans</Link>,
  },
  {
    key: browserRoutes?.contactUs,
    icon: <span className="material-symbols-outlined">contact_support</span>,
    label: <Link to={browserRoutes?.contactUs}>Contact Us</Link>,
  },
  {
    key: browserRoutes?.faqs,
    icon: <span className="material-symbols-outlined">help</span>,
    label: <Link to={browserRoutes?.faqs}>FAQs</Link>,
  },
  {
    key: browserRoutes?.staticPage,
    icon: <FileOutlined />,
    label: <Link to={browserRoutes?.staticPage}>Static Page</Link>,
  },
];

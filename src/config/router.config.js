import { lazy } from "react";
import ComingSoon from "../components/common/ComingSoon";
import PageWrapper from "../components/common/PageWrapper";
import PrivateRoute from "../components/common/RoutesWrappers/PrivateRoute";
import PublicRoute from "../components/common/RoutesWrappers/PublicRoute";
import { browserRoutes } from "./browserRoutes";

const Login = lazy(() => import("../pages/Auth/Login"));
const ForgotPassword = lazy(() => import("../pages/Auth/ForgotPassword"));
const ResetPassword = lazy(() => import("../pages/Auth/ResetPassword"));
const Profile = lazy(() => import("../pages/Profile"));
const ChangePassword = lazy(() => import("../pages/Auth/ChangePassword"));


const Users = lazy(() => import("../pages/Users"));

const StaticPage = lazy(() => import("../pages/StaticPage"));
const EditStaticPage = lazy(() => import("../pages/StaticPage/EditStaticPage"));

const ViewUser = lazy(() => import("../pages/Users/<USER>"));

const FAQs = lazy(() => import("../pages/FAQS"));
const ContactUs = lazy(() => import("../pages/ContactUs"));

// Item Management Components
const ItemManagement = lazy(() => import("../pages/ItemManagement"));
const AddEditItem = lazy(() => import("../pages/ItemManagement/AddEditItem"));
const ViewItem = lazy(() => import("../pages/ItemManagement/ViewItem"));

// Subscription Management Components
const SubscriptionManagement = lazy(() => import("../pages/SubscriptionManagement"));
const AddEditPlan = lazy(() => import("../pages/SubscriptionManagement/AddEditPlan"));

// Category Management Components
const Category = lazy(() => import("../pages/Category"));

export const routerConfig = {
  // index routes
  indexRoutes: [
    {
      path: browserRoutes?.login,
      component: (
        <PublicRoute>
          <Login />
        </PublicRoute>
      ),
    },
    {
      path: browserRoutes?.forgotPassword,
      component: (
        <PublicRoute>
          <ForgotPassword />
        </PublicRoute>
      ),
    },
    {
      path: browserRoutes?.resetPassword + "/:verificationToken",
      component: (
        <PublicRoute>
          <ResetPassword />
        </PublicRoute>
      ),
    },
  ],
  // dashboard routes
  dashboardRoutes: [
    {
      path: browserRoutes?.profile,
      component: (
        <PrivateRoute>
          <Profile />
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.changePassword,
      component: (
        <PrivateRoute>
          <ChangePassword />
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.dashboard,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <ComingSoon />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    // users
    {
      path: browserRoutes?.users,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <Users />
            {/* <ComingSoon /> */}
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.viewUser,
      component: (
        <PrivateRoute>
          {/* <ViewUser /> */}
          <PageWrapper>
            <ComingSoon />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.faqs,
      component: (
        <PrivateRoute>
          {/* <FAQs /> */}
          <PageWrapper>
            <ComingSoon />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.contactUs,
      component: (
        <PrivateRoute>
          {/* <ContactUs /> */} <PageWrapper>
            <ComingSoon />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    // Static Page
    {
      path: browserRoutes?.staticPage,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <StaticPage />
            {/* <ComingSoon /> */}
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.editStaticPage,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <ComingSoon />
          </PageWrapper>
          {/* <EditStaticPage /> */}
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.category,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <Category />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.item,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <ItemManagement />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.addItem,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <AddEditItem />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.editItem + "/:id",
      component: (
        <PrivateRoute>
          <PageWrapper>
            <AddEditItem />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.viewItem + "/:id",
      component: (
        <PrivateRoute>
          <PageWrapper>
            <ViewItem />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.subscriptionManagement,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <SubscriptionManagement />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.addSubscriptionPlan,
      component: (
        <PrivateRoute>
          <PageWrapper>
            <AddEditPlan />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
    {
      path: browserRoutes?.editSubscriptionPlan + "/:id",
      component: (
        <PrivateRoute>
          <PageWrapper>
            <AddEditPlan />
          </PageWrapper>
        </PrivateRoute>
      ),
    },
  ],
};

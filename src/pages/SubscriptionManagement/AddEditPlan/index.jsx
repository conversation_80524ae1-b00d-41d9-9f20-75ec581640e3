import React, { useState, useEffect } from "react";
import { Button, Form, Input, InputNumber, Card } from "antd";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { ArrowLeftOutlined, DollarOutlined } from "@ant-design/icons";

import PageWrapper from "../../../components/common/PageWrapper";
import PageTitle from "../../../components/common/PageTitle";
import { browserRoutes } from "../../../config/browserRoutes";
import { showError } from "../../../utils/showError";
import { showToast } from "../../../utils/showToast";

const { TextArea } = Input;

const AddEditPlan = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState(null);

  const isEditMode = !!params?.id;

  // Mock data for a subscription plan
  const mockPlanData = {
    id: 1,
    planName: "Basic Plan",
    features: "Limited storage, Basic features",
    price: 9.99,
    activeSubscriptions: 120,
  };

  useEffect(() => {
    if (isEditMode) {
      getPlanData();
    }
  }, [isEditMode]);

  const getPlanData = async () => {
    try {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        setInitialData(mockPlanData);
        form.setFieldsValue({
          planName: mockPlanData.planName,
          features: mockPlanData.features,
          price: mockPlanData.price,
        });
        setLoading(false);
      }, 500);

    } catch (err) {
      setLoading(false);
      showError(err);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // Prepare payload
      const payload = {
        planName: values.planName,
        features: values.features,
        price: values.price,
      };

      // Add ID if in edit mode
      if (isEditMode) {
        payload.id = params.id;
      }

      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        showToast.success(isEditMode ? "Plan updated successfully!" : "Plan created successfully!");
        navigate(browserRoutes?.subscriptionManagement);
      }, 1000);

    } catch (err) {
      setLoading(false);
      showError(err);
    }
  };

  return (
    <PageWrapper>
      <div className="h-full flex flex-col" style={{ minHeight: '80vh' }}>
        <div className="flex items-center mb-4">
          <Button
            icon={<ArrowLeftOutlined />}
            type="text"
            onClick={() => navigate(browserRoutes?.subscriptionManagement)}
            style={{ marginRight: '16px' }}
          />
          <PageTitle
            title={isEditMode ? "Edit Subscription Plan" : "Create Subscription Plan"}
            icon={<DollarOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />}
          />
        </div>

        <Card loading={loading && isEditMode}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              planName: "",
              features: "",
              price: 0,
            }}
          >
            <Form.Item
              name="planName"
              label="Plan Name"
              rules={[{ required: true, message: "Please enter the plan name" }]}
            >
              <Input placeholder="Enter plan name" />
            </Form.Item>

            <Form.Item
              name="features"
              label="Features"
              rules={[{ required: true, message: "Please enter the plan features" }]}
            >
              <TextArea
                placeholder="Enter plan features (comma separated)"
                rows={4}
              />
            </Form.Item>

            <Form.Item
              name="price"
              label="Price ($)"
              rules={[{ required: true, message: "Please enter the plan price" }]}
            >
              <InputNumber
                min={0}
                step={0.01}
                precision={2}
                style={{ width: '100%' }}
                placeholder="Enter plan price"
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>

            <Form.Item>
              <div className="flex justify-start">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="middle"
                >
                  Save
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </PageWrapper>
  );
};

export default AddEditPlan;

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Row, Col, Card, Statistic } from "antd";
import { PlusOutlined, DollarOutlined, SearchOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

import PageWrapper from "../../components/common/PageWrapper";
import PageTitle from "../../components/common/PageTitle";
import CustomTable from "../../components/common/CustomTable";
import CustomInput from "../../components/form/CustomInput";
import { browserRoutes } from "../../config/browserRoutes";
import DeleteConfirmModal from "../../components/common/DeleteConfirmModal";
import { showError } from "../../utils/showError";

const SubscriptionManagement = () => {
  const navigate = useNavigate();
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [searchInputValue, setSearchInputValue] = useState("");
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [statsData, setStatsData] = useState({});

  // Mock data for subscription plans
  const mockPlans = [
    {
      id: 1,
      planName: "Basic Plan",
      features: "Limited storage, Basic features",
      price: 9.99,
      activeSubscriptions: 120,
    },
    {
      id: 2,
      planName: "Standard Plan",
      features: "Expanded storage, Advanced features",
      price: 19.99,
      activeSubscriptions: 85,
    },
    {
      id: 3,
      planName: "Premium Plan",
      features: "Unlimited storage, All features, Priority support",
      price: 29.99,
      activeSubscriptions: 45,
    },
    {
      id: 4,
      planName: "Family Plan",
      features: "Shared storage, Multiple users, All features",
      price: 39.99,
      activeSubscriptions: 30,
    },
  ];

  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10,
      total: mockPlans.length,
    },
    searchTerm: "",
  });

  const columns = [
    {
      title: "Plan Name",
      dataIndex: "planName",
      key: "planName",
      width: 200, // Set fixed width
      render: (text) => <span className="font-medium">{text}</span>,
    },
    {
      title: "Features",
      dataIndex: "features",
      key: "features",
      width: 350, // Set fixed width for features column
    },
    {
      title: "Price ($)",
      dataIndex: "price",
      key: "price",
      width: 150, // Set fixed width
      render: (price) => <span>${price.toFixed(2)}</span>,
    },
    {
      title: "Active Subscriptions",
      dataIndex: "activeSubscriptions",
      key: "activeSubscriptions",
      width: 200, // Set fixed width
    },
    {
      title: "Actions",
      key: "actions",
      width: 150, // Keep fixed width for actions
      render: (_, record) => (
        <div className="flex gap-2">
          <Button
            type="primary"
            size="small"
            onClick={() => {
              navigate(`${browserRoutes?.editSubscriptionPlan}/${record?.id}`, {
                state: { id: record?.id }
              });
            }}
          >
            Edit
          </Button>
          <Button
            type="primary"
            danger
            size="small"
            onClick={() => {
              setSelectedPlan(record);
              setOpenDeleteModal(true);
            }}
          >
            Delete
          </Button>
        </div>
      ),
      fixed: "right", // Add this to fix the Actions column to the right
    },
  ];

  const handleAddPlan = () => {
    navigate(browserRoutes?.addSubscriptionPlan);
  };

  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchInputValue(value);

    setTableParams({
      ...tableParams,
      searchTerm: value,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleClearSearch = () => {
    setSearchInputValue("");
    setTableParams({
      ...tableParams,
      searchTerm: "",
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Filter out the deleted plan
        const updatedPlans = tableData.filter(plan => plan.id !== selectedPlan.id);
        setTableData(updatedPlans);

        // Update stats
        calculateStats(updatedPlans);

        setDeleteLoading(false);
        setOpenDeleteModal(false);
      }, 1000);

    } catch (err) {
      setDeleteLoading(false);
      showError(err);
    }
  };

  const getPlansData = async () => {
    try {
      setTableLoading(true);

      // Simulate API call with mock data
      setTimeout(() => {
        // Filter data based on search term if needed
        const filteredData = tableParams.searchTerm
          ? mockPlans.filter(plan =>
            plan.planName.toLowerCase().includes(tableParams.searchTerm.toLowerCase())
          )
          : mockPlans;

        setTableData(filteredData);
        calculateStats(filteredData);

        // Update pagination total without changing the search term
        setTableParams(prev => ({
          ...prev,
          pagination: {
            ...prev.pagination,
            total: filteredData.length,
          },
        }));

        setTableLoading(false);
      }, 500);
    } catch (err) {
      setTableLoading(false);
      showError(err);
    }
  };

  const calculateStats = (plans) => {
    const totalSubscriptions = plans.reduce((sum, plan) => sum + plan.activeSubscriptions, 0);
    const totalRevenue = plans.reduce((sum, plan) => sum + (plan.price * plan.activeSubscriptions), 0);

    setStatsData({
      totalPlans: plans.length,
      totalSubscriptions,
      totalRevenue: totalRevenue.toFixed(2)
    });
  };

  useEffect(() => {
    getPlansData();
  }, [tableParams.searchTerm]);

  return (
    <PageWrapper>
      <div className="h-full flex flex-col" style={{ minHeight: '80vh' }}>
        <div className="flex justify-between items-center">
          <PageTitle title="Subscription Management" icon={<DollarOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />} />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddPlan}
          >
            Add Plan
          </Button>
        </div>

        {/* Statistics Cards */}
        <Row gutter={16} className="my-4">
          <Col span={8}>
            <Card>
              <Statistic
                title="Total Plans"
                value={statsData.totalPlans || 0}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="Active Subscriptions"
                value={statsData.totalSubscriptions || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          {/* <Col span={8}>
            <Card>
              <Statistic
                title="Monthly Revenue"
                value={statsData.totalRevenue || 0}
                prefix="$"
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col> */}
        </Row>

        <div className="pt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <CustomInput
              placeholder={"Search by plan name"}
              prefix={<SearchOutlined className="text-[#d9d9d9]" />}
              value={searchInputValue}
              onChange={handleSearchInputChange}
              allowClear
              onClear={handleClearSearch}
            />
          </div>
        </div>

        <div className="flex-1 overflow-hidden pt-4" style={{ minHeight: '500px' }}>
          <CustomTable
            scrollX={900} // Sum of all column widths plus some buffer
            rowKey={(record) => record?.id}
            columns={columns}
            dataSource={tableData}
            loading={tableLoading}
            onChange={(pagination, filters, sorter) => {
              setTableParams({
                ...tableParams,
                pagination,
                ...filters,
                ...sorter,
              });
            }}
            pagination={tableParams?.pagination}
          />
        </div>
      </div>
      {openDeleteModal && (
        <DeleteConfirmModal
          title={"Delete Subscription Plan"}
          description={`Are you sure you want to delete the "${selectedPlan?.planName}" plan? This will affect ${selectedPlan?.activeSubscriptions} active subscriptions.`}
          open={openDeleteModal}
          onCancel={() => setOpenDeleteModal(false)}
          handleDelete={handleDelete}
          deleteLoading={deleteLoading}
        />
      )}
    </PageWrapper>
  );
};

export default SubscriptionManagement;

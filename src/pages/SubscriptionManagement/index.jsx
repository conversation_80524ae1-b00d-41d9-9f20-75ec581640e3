import React, { useState, useEffect } from "react";
import { Row, Col, Card, Statistic, Tag, Button, Modal, Form, Input, Select, message } from "antd";
import { DollarOutlined, SearchOutlined, UserOutlined, CrownOutlined, BellOutlined, SendOutlined } from "@ant-design/icons";
import moment from "moment";
import { getActiveNotificationTemplates } from "../../services/notificationTemplates";

import PageWrapper from "../../components/common/PageWrapper";
import PageTitle from "../../components/common/PageTitle";
import CustomTable from "../../components/common/CustomTable";
import CustomInput from "../../components/form/CustomInput";
import { showError } from "../../utils/showError";

const SubscriptionManagement = () => {
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [searchInputValue, setSearchInputValue] = useState("");
  const [statsData, setStatsData] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [notificationModalOpen, setNotificationModalOpen] = useState(false);
  const [sendingNotification, setSendingNotification] = useState(false);
  const [notificationTemplates, setNotificationTemplates] = useState([]);
  const [form] = Form.useForm();

  // Single subscription plan data
  const subscriptionPlan = {
    planName: "PantryPal Premium",
    description: "Complete pantry management solution with smart features",
    price: 9.99,
    currency: "USD",
    billingCycle: "monthly",
    freeTrialDays: 7,
    // features: [
    //   "Unlimited pantry items tracking",
    //   "Smart expiry notifications",
    //   "Recipe suggestions based on available items",
    //   "Shopping list automation",
    //   "Nutritional insights",
    //   "Family sharing (up to 5 members)",
    //   "Priority customer support"
    // ]
  };

  // Load notification templates
  const loadNotificationTemplates = async () => {
    try {
      const templates = await getActiveNotificationTemplates();
      // Add custom option
      const templatesWithCustom = [
        ...templates,
        {
          type: "custom",
          title: "Custom Message",
          subject: "",
          message: ""
        }
      ];
      setNotificationTemplates(templatesWithCustom);
    } catch (error) {
      console.error("Failed to load notification templates:", error);
      // Fallback to basic templates
      setNotificationTemplates([
        {
          type: "custom",
          title: "Custom Message",
          subject: "",
          message: ""
        }
      ]);
    }
  };

  // Mock user subscription data with expiry dates
  const mockUserSubscriptions = [
    {
      id: 1,
      fullName: "John Doe",
      email: "<EMAIL>",
      subscriptionStatus: "trial",
      subscriptionStartDate: "2024-06-25",
      subscriptionExpiryDate: "2024-07-01", // Expires tomorrow
      isTrialUser: true,
      daysUntilExpiry: 1
    },
    {
      id: 2,
      fullName: "Jane Smith",
      email: "<EMAIL>",
      subscriptionStatus: "trial",
      subscriptionStartDate: "2024-06-24",
      subscriptionExpiryDate: "2024-07-02", // Expires in 2 days
      isTrialUser: true,
      daysUntilExpiry: 2
    },
    {
      id: 3,
      fullName: "Mike Johnson",
      email: "<EMAIL>",
      subscriptionStatus: "active",
      subscriptionStartDate: "2024-05-15",
      subscriptionExpiryDate: "2024-07-15", // Expires in 15 days
      isTrialUser: false,
      daysUntilExpiry: 15
    },
    {
      id: 4,
      fullName: "Sarah Wilson",
      email: "<EMAIL>",
      subscriptionStatus: "trial",
      subscriptionStartDate: "2024-06-26",
      subscriptionExpiryDate: "2024-07-03", // Expires in 3 days
      isTrialUser: true,
      daysUntilExpiry: 3
    },
    {
      id: 5,
      fullName: "David Brown",
      email: "<EMAIL>",
      subscriptionStatus: "active",
      subscriptionStartDate: "2024-04-20",
      subscriptionExpiryDate: "2024-08-20", // Expires in 51 days
      isTrialUser: false,
      daysUntilExpiry: 51
    },
    {
      id: 6,
      fullName: "Lisa Davis",
      email: "<EMAIL>",
      subscriptionStatus: "trial",
      subscriptionStartDate: "2024-06-27",
      subscriptionExpiryDate: "2024-07-04", // Expires in 4 days
      isTrialUser: true,
      daysUntilExpiry: 4
    }
  ];

  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    searchTerm: "",
  });

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedUsers(selectedRows);
    },
    getCheckboxProps: (record) => ({
      name: record.fullName,
    }),
  };

  const columns = [
    {
      title: "User Name",
      dataIndex: "fullName",
      key: "fullName",
      width: 200,
      render: (text) => <span className="font-medium">{text}</span>,
      sorter: () => true,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 280,
      render: (text) => <span>{text}</span>,
    },
    {
      title: "Status",
      dataIndex: "subscriptionStatus",
      key: "subscriptionStatus",
      width: 120,
      align: "center",
      render: (_, record) => {
        const isExpiringSoon = record.daysUntilExpiry <= 3;
        const color = record.isTrialUser
          ? (isExpiringSoon ? "volcano" : "orange")
          : (isExpiringSoon ? "red" : "green");

        return (
          <Tag color={color}>
            {record.isTrialUser ? "Free Trial" : "Premium"}
          </Tag>
        );
      },
    },
    {
      title: "Expiry Date",
      dataIndex: "subscriptionExpiryDate",
      key: "subscriptionExpiryDate",
      width: 150,
      align: "center",
      render: (date) => moment(date).format("MMM DD, YYYY"),
      sorter: () => true,
    },
    {
      title: "Days Until Expiry",
      dataIndex: "daysUntilExpiry",
      key: "daysUntilExpiry",
      width: 150,
      align: "center",
      render: (days) => {
        const color = days <= 3 ? "red" : days <= 7 ? "orange" : "green";
        return <span style={{ color }}>{days} days</span>;
      },
      sorter: () => true,
    },
  ];

  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchInputValue(value);

    setTableParams({
      ...tableParams,
      searchTerm: value,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleClearSearch = () => {
    setSearchInputValue("");
    setTableParams({
      ...tableParams,
      searchTerm: "",
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleSendNotification = () => {
    if (selectedUsers.length === 0) {
      message.warning("Please select at least one user to send notifications.");
      return;
    }
    setNotificationModalOpen(true);
  };

  const handleNotificationSubmit = async (values) => {
    try {
      setSendingNotification(true);

      // Simulate API call to send notifications
      setTimeout(() => {
        const userNames = selectedUsers.map(user => user.fullName).join(", ");
        message.success(`Notification sent successfully to ${selectedUsers.length} user(s): ${userNames}`);

        // Reset form and close modal
        form.resetFields();
        setNotificationModalOpen(false);
        setSelectedRowKeys([]);
        setSelectedUsers([]);
        setSendingNotification(false);
      }, 1500);

    } catch (error) {
      setSendingNotification(false);
      message.error("Failed to send notification. Please try again.");
    }
  };

  const handleTemplateChange = (templateType) => {
    const template = notificationTemplates.find(t => t.type === templateType);
    if (template && templateType !== 'custom') {
      form.setFieldsValue({
        subject: template.subject,
        message: template.message
      });
    } else if (templateType === 'custom') {
      form.setFieldsValue({
        subject: "",
        message: ""
      });
    }
  };

  const getUserSubscriptionsData = async () => {
    try {
      setTableLoading(true);

      // Simulate API call with mock data
      setTimeout(() => {
        // Sort by expiry date (nearest expiry first)
        const sortedData = [...mockUserSubscriptions].sort((a, b) => a.daysUntilExpiry - b.daysUntilExpiry);

        // Filter data based on search term if needed
        const filteredData = tableParams.searchTerm
          ? sortedData.filter(user =>
            user.fullName.toLowerCase().includes(tableParams.searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(tableParams.searchTerm.toLowerCase())
          )
          : sortedData;

        setTableData(filteredData);
        calculateStats(sortedData);

        // Update pagination total without changing the search term
        setTableParams(prev => ({
          ...prev,
          pagination: {
            ...prev.pagination,
            total: filteredData.length,
          },
        }));

        setTableLoading(false);
      }, 500);
    } catch (err) {
      setTableLoading(false);
      showError(err);
    }
  };

  const calculateStats = (users) => {
    const totalUsers = users.length;
    const trialUsers = users.filter(user => user.isTrialUser).length;
    const premiumUsers = users.filter(user => !user.isTrialUser).length;
    const expiringSoon = users.filter(user => user.daysUntilExpiry <= 7).length;
    const monthlyRevenue = premiumUsers * subscriptionPlan.price;

    setStatsData({
      totalUsers,
      trialUsers,
      premiumUsers,
      expiringSoon,
      monthlyRevenue: monthlyRevenue.toFixed(2)
    });
  };

  useEffect(() => {
    getUserSubscriptionsData();
    loadNotificationTemplates();
  }, [tableParams.searchTerm]);

  return (
    <PageWrapper>
      <div className="h-full flex flex-col" style={{ minHeight: '80vh' }}>
        <div className="flex justify-between items-center">
          <PageTitle title="Subscription Management" icon={<DollarOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />} />
        </div>

        {/* Subscription Plan Information Card */}
        <Card className="my-4" title={
          <div className="flex items-center gap-2">
            <CrownOutlined style={{ color: '#faad14' }} />
            <span>{subscriptionPlan.planName}</span>
          </div>
        }>
          <Row gutter={16}>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">${subscriptionPlan.price}</div>
                <div className="text-gray-500">per month</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{subscriptionPlan.freeTrialDays}</div>
                <div className="text-gray-500">days free trial</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="text-gray-600">{subscriptionPlan.description}</div>
              {/* <div className="mt-2 text-sm text-gray-500">
                Features: {subscriptionPlan.features.slice(0, 3).join(", ")} and more...
              </div> */}
            </Col>
          </Row>
        </Card>

        {/* Statistics Cards */}
        <Row gutter={16} className="mb-4">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={statsData.totalUsers || 0}
                valueStyle={{ color: '#1890ff' }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Trial Users"
                value={statsData.trialUsers || 0}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Premium Users"
                value={statsData.premiumUsers || 0}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Expiring Soon"
                value={statsData.expiringSoon || 0}
                valueStyle={{ color: '#ff4d4f' }}
                suffix="(≤7 days)"
              />
            </Card>
          </Col>
        </Row>

        <div className="pt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <CustomInput
              placeholder={"Search by name or email"}
              prefix={<SearchOutlined className="text-[#d9d9d9]" />}
              value={searchInputValue}
              onChange={handleSearchInputChange}
              allowClear
              onClear={handleClearSearch}
            />
            {selectedUsers.length > 0 && (
              <Button
                type="primary"
                icon={<BellOutlined />}
                onClick={handleSendNotification}
              >
                Send Notification ({selectedUsers.length})
              </Button>
            )}
          </div>
          <div className="text-sm text-gray-500">
            Users sorted by expiry date (nearest first)
          </div>
        </div>

        <div className="flex-1 overflow-hidden pt-4" style={{ minHeight: '500px' }}>
          <CustomTable
            scrollX={1000}
            rowKey={(record) => record?.id}
            columns={columns}
            dataSource={tableData}
            loading={tableLoading}
            rowSelection={rowSelection}
            onChange={(pagination, filters, sorter) => {
              setTableParams({
                ...tableParams,
                pagination,
                ...filters,
                ...sorter,
              });
            }}
            pagination={tableParams?.pagination}
          />
        </div>
      </div>

      {/* Notification Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <BellOutlined />
            <span>Send Notification</span>
          </div>
        }
        open={notificationModalOpen}
        onCancel={() => {
          setNotificationModalOpen(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <div className="mb-4">
          <p className="text-gray-600">
            Sending notification to <strong>{selectedUsers.length}</strong> selected user(s):
          </p>
          <div className="bg-gray-50 p-2 rounded mt-2 max-h-20 overflow-y-auto">
            {selectedUsers.map(user => (
              <Tag key={user.id} className="mb-1">
                {user.fullName}
              </Tag>
            ))}
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleNotificationSubmit}
        >
          <Form.Item
            label="Notification Template"
            name="template"
          >
            <Select
              placeholder="Select a template or choose custom"
              onChange={handleTemplateChange}
              options={notificationTemplates.map(template => ({
                label: template.title,
                value: template.type
              }))}
            />
          </Form.Item>

          <Form.Item
            label="Subject"
            name="subject"
            rules={[{ required: true, message: 'Please enter a subject' }]}
          >
            <Input placeholder="Enter notification subject" />
          </Form.Item>

          <Form.Item
            label="Message"
            name="message"
            rules={[{ required: true, message: 'Please enter a message' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="Enter notification message"
              showCount
              maxLength={500}
            />
          </Form.Item>

          <div className="text-xs text-gray-500 mb-4">
            <strong>Available placeholders:</strong> {"{userName}"}, {"{expiryDate}"}
          </div>

          <div className="flex justify-end gap-2">
            <Button
              onClick={() => {
                setNotificationModalOpen(false);
                form.resetFields();
              }}
              disabled={sendingNotification}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={sendingNotification}
              icon={<SendOutlined />}
            >
              Send Notification
            </Button>
          </div>
        </Form>
      </Modal>
    </PageWrapper>
  );
};

export default SubscriptionManagement;

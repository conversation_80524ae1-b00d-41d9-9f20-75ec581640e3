import React, { useEffect, useState } from "react";
import { Button, Form, Input, DatePicker, InputNumber, Select, Spin } from "antd";
import { CaretLeftOutlined, SaveOutlined, AppstoreOutlined } from "@ant-design/icons";
import { useLocation, useNavigate } from "react-router-dom";
import moment from "moment";

import PageWrapper from "../../../components/common/PageWrapper";
import PageTitle from "../../../components/common/PageTitle";
import { showToast } from "../../../utils/showToast";
import { showError } from "../../../utils/showError";

const { TextArea } = Input;
const { Option } = Select;

const AddEditItem = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(false);
  const { state } = useLocation();
  const navigate = useNavigate();
  const isEdit = !!state?.id;

  const locationOptions = [
    { value: "Cabinet A", label: "Cabinet A" },
    { value: "Cabinet B", label: "Cabinet B" },
    { value: "Drawer C", label: "Drawer C" },
    { value: "Shelf D", label: "Shelf D" },
    { value: "Refrigerator", label: "Refrigerator" },
  ];

  useEffect(() => {
    if (isEdit) {
      fetchItemDetails();
    }
  }, [state]);

  const fetchItemDetails = async () => {
    try {
      setInitialLoading(true);
      
      // Mock data for item details
      const mockItemData = {
        id: state?.id,
        itemName: "Paracetamol",
        quantity: 100,
        expiryDate: "2025-12-31",
        location: "Cabinet A",
        description: "Pain reliever and fever reducer medication",
        manufacturer: "ABC Pharmaceuticals",
        batchNumber: "BATCH123456",
        minStockLevel: 20,
        notes: "Keep away from direct sunlight and moisture. Store at room temperature."
      };
      
      // Simulate API call
      setTimeout(() => {
        form.setFieldsValue({
          ...mockItemData,
          expiryDate: mockItemData.expiryDate ? moment(mockItemData.expiryDate) : null,
        });
        setInitialLoading(false);
      }, 500);
      
      // When ready to use real API:
      // const res = await Api("GET", ApiRoute?.getItemDetails, {
      //   id: state?.id,
      // });
      // if (res?.data?.status) {
      //   form.setFieldsValue({
      //     ...res.data.data,
      //     expiryDate: res.data.data.expiryDate ? moment(res.data.data.expiryDate) : null,
      //   });
      // } else {
      //   showToast.error(res?.data?.message);
      // }
    } catch (err) {
      setInitialLoading(false);
      showError(err);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      const formData = {
        ...values,
        expiryDate: values.expiryDate ? values.expiryDate.format("YYYY-MM-DD") : null,
      };
      
      if (isEdit) {
        formData.id = state.id;
      }
      
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        showToast.success(isEdit ? "Item updated successfully" : "Item added successfully");
        navigate(-1);
      }, 1000);
      
      // When ready to use real API:
      // const res = await Api(
      //   isEdit ? "PUT" : "POST",
      //   isEdit ? ApiRoute?.updateItem : ApiRoute?.addItem,
      //   formData
      // );
      // if (res?.data?.status) {
      //   showToast.success(res?.data?.message);
      //   navigate(-1);
      // } else {
      //   setLoading(false);
      //   showToast.error(res?.data?.message);
      // }
    } catch (err) {
      setLoading(false);
      showError(err);
    }
  };

  return (
    <PageWrapper>
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2">
          <Button
            icon={<CaretLeftOutlined />}
            onClick={() => navigate(-1)}
            className="flex items-center"
          >
            Back
          </Button>
          <PageTitle 
            title={isEdit ? "Edit Item" : "Add Item"} 
            icon={<AppstoreOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />} 
          />
        </div>
        
        {initialLoading ? (
          <div className="h-full w-full flex justify-center items-center pt-10">
            <Spin spinning={true} />
          </div>
        ) : (
          <div className="pt-6">
            <div className="bg-white p-6 rounded-md shadow-sm">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  quantity: 0,
                  minStockLevel: 0,
                }}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                  <Form.Item
                    name="itemName"
                    label="Item Name"
                    rules={[{ required: true, message: "Please enter item name" }]}
                  >
                    <Input placeholder="Enter item name" />
                  </Form.Item>
                  
                  <Form.Item
                    name="quantity"
                    label="Quantity"
                    rules={[{ required: true, message: "Please enter quantity" }]}
                  >
                    <InputNumber min={0} style={{ width: "100%" }} placeholder="Enter quantity" />
                  </Form.Item>
                  
                  <Form.Item
                    name="expiryDate"
                    label="Expiry Date"
                  >
                    <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
                  </Form.Item>
                  
                  <Form.Item
                    name="location"
                    label="Location"
                    rules={[{ required: true, message: "Please select location" }]}
                  >
                    <Select placeholder="Select location">
                      {locationOptions.map(option => (
                        <Option key={option.value} value={option.value}>{option.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="manufacturer"
                    label="Manufacturer"
                  >
                    <Input placeholder="Enter manufacturer" />
                  </Form.Item>
                  
                  <Form.Item
                    name="batchNumber"
                    label="Batch Number"
                  >
                    <Input placeholder="Enter batch number" />
                  </Form.Item>
                  
                  <Form.Item
                    name="minStockLevel"
                    label="Minimum Stock Level"
                  >
                    <InputNumber min={0} style={{ width: "100%" }} placeholder="Enter minimum stock level" />
                  </Form.Item>
                </div>
                
                <Form.Item
                  name="description"
                  label="Description"
                >
                  <TextArea rows={3} placeholder="Enter item description" />
                </Form.Item>
                
                <Form.Item
                  name="notes"
                  label="Notes"
                >
                  <TextArea rows={3} placeholder="Enter additional notes" />
                </Form.Item>
                
                <Form.Item className="mb-0 flex justify-end">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    {isEdit ? "Update Item" : "Save Item"}
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default AddEditItem;
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Spin } from "antd";
import { CaretLeftOutlined, AppstoreOutlined } from "@ant-design/icons";
import { useLocation, useNavigate } from "react-router-dom";

import PageWrapper from "../../../components/common/PageWrapper";
import PageTitle from "../../../components/common/PageTitle";
import { showToast } from "../../../utils/showToast";
import { showError } from "../../../utils/showError";

const ViewItem = () => {
  const [pageData, setPageData] = useState(null);
  const [pageLoading, setPageLoading] = useState(true);
  const { state } = useLocation();
  const navigate = useNavigate();

  const getPageData = async () => {
    try {
      setPageLoading(true);
      
      // Mock data for item details
      const mockItemData = {
        id: state?.id,
        itemName: "Paracetamol",
        quantity: 100,
        expiryDate: "2025-12-31",
        location: "Cabinet A",
        description: "Pain reliever and fever reducer medication",
        manufacturer: "ABC Pharmaceuticals",
        batchNumber: "BATCH123456",
        dateAdded: "2023-05-15",
        lastUpdated: "2023-11-20",
        minStockLevel: 20,
        notes: "Keep away from direct sunlight and moisture. Store at room temperature."
      };
      
      // Simulate API call
      setTimeout(() => {
        setPageData(mockItemData);
        setPageLoading(false);
      }, 500);
      
      // When ready to use real API:
      // const res = await Api("GET", ApiRoute?.getItemDetails, {
      //   id: state?.id,
      // });
      // if (res?.data?.status) {
      //   setPageData(res?.data?.data);
      // } else {
      //   showToast.error(res?.data?.message);
      // }
    } catch (err) {
      setPageLoading(false);
      showError(err);
    }
  };

  useEffect(() => {
    if (state?.id) {
      getPageData();
    }
  }, [state]);

  return (
    <PageWrapper>
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2">
          <Button
            icon={<CaretLeftOutlined />}
            onClick={() => navigate(-1)}
            className="flex items-center"
          >
            Back
          </Button>
          <PageTitle title="Item Details" icon={<AppstoreOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />} />
        </div>
        <div className="pt-4">
          {pageLoading ? (
            <div className="h-full w-full flex justify-center items-center">
              <Spin spinning={true} />
            </div>
          ) : (
            <div className="bg-white p-6 rounded-md shadow-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">{pageData?.itemName}</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="font-medium w-40">Quantity:</span>
                      <span>{pageData?.quantity}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Expiry Date:</span>
                      <span>{pageData?.expiryDate || "N/A"}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Location:</span>
                      <span>{pageData?.location}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Manufacturer:</span>
                      <span>{pageData?.manufacturer}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Batch Number:</span>
                      <span>{pageData?.batchNumber}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Additional Information</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="font-medium w-40">Date Added:</span>
                      <span>{pageData?.dateAdded}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Last Updated:</span>
                      <span>{pageData?.lastUpdated}</span>
                    </div>
                    <div className="flex">
                      <span className="font-medium w-40">Min Stock Level:</span>
                      <span>{pageData?.minStockLevel}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-gray-700">{pageData?.description}</p>
              </div>
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Notes</h3>
                <p className="text-gray-700">{pageData?.notes}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  );
};

export default ViewItem;
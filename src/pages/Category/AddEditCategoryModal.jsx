import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Select, Upload, Button, Switch, Divider, Card, Space } from "antd";
import { PlusOutlined, DeleteOutlined, UploadOutlined } from "@ant-design/icons";

const { Option } = Select;

const AddEditCategoryModal = ({ open, onCancel, editData, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [subcategories, setSubcategories] = useState([]);
  const [imageFileList, setImageFileList] = useState([]);

  useEffect(() => {
    if (editData) {
      // Populate form with edit data
      form.setFieldsValue({
        title: editData.title,
        unit: editData.unit,
        status: editData.status,
      });
      
      // Set subcategories
      setSubcategories(editData.subcategories || []);
      
      // Set image
      if (editData.image) {
        setImageFileList([{
          uid: '-1',
          name: 'category-image',
          status: 'done',
          url: editData.image,
        }]);
      }
    } else {
      // Reset form for add mode
      form.resetFields();
      setSubcategories([]);
      setImageFileList([]);
    }
  }, [editData, form, open]);

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const categoryData = {
        ...values,
        subcategories: subcategories,
        image: imageFileList[0]?.url || imageFileList[0]?.response?.url || '',
        id: editData?.id || Date.now(),
        createdAt: editData?.createdAt || new Date().toISOString(),
      };
      
      console.log('Category data:', categoryData);
      
      onSuccess();
    } catch (error) {
      console.error('Error saving category:', error);
    } finally {
      setLoading(false);
    }
  };

  const addSubcategory = () => {
    setSubcategories([
      ...subcategories,
      {
        id: Date.now(),
        title: '',
        image: '',
        status: true,
      }
    ]);
  };

  const updateSubcategory = (index, field, value) => {
    const updated = [...subcategories];
    updated[index] = { ...updated[index], [field]: value };
    setSubcategories(updated);
  };

  const removeSubcategory = (index) => {
    const updated = subcategories.filter((_, i) => i !== index);
    setSubcategories(updated);
  };

  const handleImageChange = ({ fileList }) => {
    setImageFileList(fileList);
  };

  const handleSubcategoryImageChange = (index, { fileList }) => {
    const imageUrl = fileList[0]?.url || fileList[0]?.response?.url || '';
    updateSubcategory(index, 'image', imageUrl);
  };

  const uploadProps = {
    name: 'file',
    listType: 'picture-card',
    className: 'category-uploader',
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
    },
    beforeUpload: () => false, // Prevent auto upload
    maxCount: 1,
  };

  return (
    <Modal
      title={editData ? "Edit Category" : "Add Category"}
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: true,
          unit: 'units'
        }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="title"
            label="Category Name"
            rules={[
              { required: true, message: "Please enter category name" },
              { min: 2, message: "Category name must be at least 2 characters" }
            ]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="unit"
            label="Unit of Quantity"
            rules={[{ required: true, message: "Please select unit of quantity" }]}
          >
            <Select placeholder="Select unit">
              <Option value="units">Units</Option>
              <Option value="lbs">Pounds (lbs)</Option>
              <Option value="kg">Kilograms (kg)</Option>
              <Option value="oz">Ounces (oz)</Option>
              <Option value="grams">Grams</Option>
              <Option value="liters">Liters</Option>
              <Option value="ml">Milliliters (ml)</Option>
              <Option value="pieces">Pieces</Option>
              <Option value="packages">Packages</Option>
            </Select>
          </Form.Item>
        </div>

        <Form.Item label="Category Image">
          <Upload
            {...uploadProps}
            fileList={imageFileList}
            onChange={handleImageChange}
          >
            {imageFileList.length < 1 && (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload Image</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        <Form.Item name="status" label="Status" valuePropName="checked">
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>

        <Divider>Subcategories</Divider>

        <div className="space-y-4">
          {subcategories.map((subcategory, index) => (
            <Card key={index} size="small" className="border-gray-200">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Subcategory name"
                    value={subcategory.title}
                    onChange={(e) => updateSubcategory(index, 'title', e.target.value)}
                  />
                </div>
                <div className="w-32">
                  <Upload
                    {...uploadProps}
                    fileList={subcategory.image ? [{
                      uid: `sub-${index}`,
                      name: 'subcategory-image',
                      status: 'done',
                      url: subcategory.image,
                    }] : []}
                    onChange={(info) => handleSubcategoryImageChange(index, info)}
                  >
                    {!subcategory.image && (
                      <div className="text-xs">
                        <UploadOutlined />
                        <div>Image</div>
                      </div>
                    )}
                  </Upload>
                </div>
                <Switch
                  size="small"
                  checked={subcategory.status}
                  onChange={(checked) => updateSubcategory(index, 'status', checked)}
                />
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeSubcategory(index)}
                />
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-4">
          <Button
            type="dashed"
            onClick={addSubcategory}
            icon={<PlusOutlined />}
            className="w-full"
          >
            Add Subcategory
          </Button>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button onClick={onCancel}>
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            {editData ? "Update Category" : "Add Category"}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AddEditCategoryModal;

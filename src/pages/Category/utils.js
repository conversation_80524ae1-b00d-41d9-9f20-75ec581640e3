import { Switch, Tag, Avatar } from "antd";
import { <PERSON>Button, DeleteButton } from "../../components/common/TableButtons";

export const getColumns = ({
  handleEditCategory,
  setOpenDeleteModal,
  setDeletedRecordId,
  handleStatusChange,
}) => {
  return [
    {
      title: <span className="w-full flex justify-center">Image</span>,
      dataIndex: "image",
      key: "image",
      width: 100,
      render: (value, record) => {
        return (
          <div className="w-full flex justify-center">
            <Avatar
              className="w-16 h-16"
              src={value}
              alt={record.title}
              shape="square"
            />
          </div>
        );
      },
    },
    {
      title: "Category Name",
      dataIndex: "title",
      key: "title",
      width: 250,
      render: (value, record) => {
        return (
          <div className="flex flex-col">
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">
              {record.subcategories?.length || 0} subcategories
            </div>
          </div>
        );
      },
      sorter: (a, b) => a.title.localeCompare(b.title),
    },
    {
      title: "Unit of Quantity",
      dataIndex: "unit",
      key: "unit",
      width: 150,
      align: "center",
      render: (value) => {
        return (
          <Tag color="blue" className="text-center">
            {value}
          </Tag>
        );
      },
    },
    {
      title: "Subcategories",
      key: "subcategories",
      width: 200,
      align: "center",
      render: (_, record) => {
        const subcategoryCount = record.subcategories?.length || 0;
        const activeSubcategories = record.subcategories?.filter(sub => sub.status)?.length || 0;
        
        return (
          <div className="flex flex-col items-center">
            <div className="text-lg font-semibold text-blue-600">
              {subcategoryCount}
            </div>
            <div className="text-xs text-gray-500">
              {activeSubcategories} active
            </div>
          </div>
        );
      },
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      align: "center",
      render: (value) => {
        return (
          <div className="text-sm">
            {new Date(value).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </div>
        );
      },
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: <span className="w-full flex justify-center">Status</span>,
      key: "status",
      dataIndex: "status",
      width: 100,
      align: "center",
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center">
            <Switch
              size="small"
              checked={record.status}
              onChange={(checked) => handleStatusChange(record.id, checked)}
            />
          </div>
        );
      },
    },
    {
      title: <span className="w-full flex justify-center">Actions</span>,
      key: "actions",
      width: 120,
      align: "center",
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center gap-2">
            <EditButton
              onClick={() => handleEditCategory(record)}
              tooltip="Edit Category"
            />
            <DeleteButton
              onClick={() => {
                setDeletedRecordId(record.id);
                setOpenDeleteModal(true);
              }}
              tooltip="Delete Category"
            />
          </div>
        );
      },
    },
  ];
};

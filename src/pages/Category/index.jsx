import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Switch } from "antd";
import { SearchOutlined, PlusOutlined } from "@ant-design/icons";

import PageWrapper from "../../components/common/PageWrapper";
import CustomTable from "../../components/common/CustomTable";
import CustomInput from "../../components/form/CustomInput";
import PageTitle from "../../components/common/PageTitle";
import DeleteConfirmModal from "../../components/common/DeleteConfirmModal";
import { getColumns } from "./utils";
import AddEditCategoryModal from "./AddEditCategoryModal";
import { categoryMockData } from "./mockData";

const Category = () => {
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [searchInputValue, setSearchInputValue] = useState("");
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    searchTerm: "",
  });

  // Modal states
  const [openAddEditModal, setOpenAddEditModal] = useState(false);
  const [editModalData, setEditModalData] = useState(null);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deletedRecordId, setDeletedRecordId] = useState(null);

  // Load mock data
  useEffect(() => {
    getTableData();
  }, [tableParams]);

  const getTableData = async (manageLoading = true) => {
    try {
      if (manageLoading) {
        setTableLoading(true);
      }

      // Simulate API call with mock data
      let filteredData = categoryMockData;

      // Apply search filter
      if (tableParams.searchTerm) {
        filteredData = filteredData.filter(category =>
          category.title.toLowerCase().includes(tableParams.searchTerm.toLowerCase())
        );
      }

      setTableData(filteredData);

      // Update pagination total without triggering useEffect
      setTableParams(prev => {
        // Only update if the total has actually changed to prevent infinite loops
        if (prev.pagination.total !== filteredData.length) {
          return {
            ...prev,
            pagination: {
              ...prev.pagination,
              total: filteredData.length,
            },
          };
        }
        return prev;
      });
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      if (manageLoading) {
        setTableLoading(false);
      }
    }
  };

  const handleSearchInputChange = (e) => {
    setSearchInputValue(e.target.value);
  };

  const handleSearch = () => {
    setTableParams(prev => ({
      ...prev,
      searchTerm: searchInputValue,
      pagination: { ...prev.pagination, current: 1 },
    }));
  };

  const handleTableChange = (pagination, filters, sorter) => {
    setTableParams({
      pagination,
      filters,
      ...sorter,
    });
  };

  const handleAddCategory = () => {
    setEditModalData(null);
    setOpenAddEditModal(true);
  };

  const handleEditCategory = (record) => {
    setEditModalData(record);
    setOpenAddEditModal(true);
  };

  const handleDeleteCategory = async () => {
    try {
      setDeleteLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove from table data
      setTableData(prev => prev.filter(item => item.id !== deletedRecordId));
      setOpenDeleteModal(false);
      setDeletedRecordId(null);
    } catch (error) {
      console.error("Error deleting category:", error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleStatusChange = async (categoryId, newStatus) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update status in table data
      setTableData(prev => prev.map(item =>
        item.id === categoryId ? { ...item, status: newStatus } : item
      ));
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const handleModalSuccess = () => {
    setOpenAddEditModal(false);
    setEditModalData(null);
    getTableData(false);
  };

  return (
    <PageWrapper isScrollable={false}>
      <div className="h-full flex flex-col overflow-hidden">
        <div className="flex justify-between items-center gap-3">
          <PageTitle
            title="Category Management"
            icon={<span className="material-symbols-outlined">category</span>}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddCategory}
          >
            Add Category
          </Button>
        </div>

        <div className="pt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <CustomInput
              placeholder="Search by category name"
              prefix={<SearchOutlined className="text-[#d9d9d9]" />}
              value={searchInputValue}
              onChange={handleSearchInputChange}
              onPressEnter={handleSearch}
              allowClear
            />
            <Button onClick={handleSearch}>Search</Button>
          </div>
        </div>

        <div className="flex-1 overflow-hidden pt-4">
          <CustomTable
            rowKey={(record) => record?.id}
            scrollX={1200}
            columns={getColumns({
              handleEditCategory,
              setOpenDeleteModal,
              setDeletedRecordId,
              handleStatusChange,
            })}
            dataSource={tableData}
            onChange={handleTableChange}
            pagination={tableParams?.pagination}
            loading={tableLoading}
            expandable={{
              expandedRowRender: (record) => (
                <div className="p-4 bg-gray-50">
                  <h4 className="font-semibold mb-3">Subcategories:</h4>
                  {record.subcategories && record.subcategories.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {record.subcategories.map((sub, index) => (
                        <div key={index} className="bg-white p-3 rounded border">
                          <div className="flex items-center gap-3">
                            <img
                              src={sub.image}
                              alt={sub.title}
                              className="w-12 h-12 object-cover rounded"
                            />
                            <div className="flex-1">
                              <div className="font-medium">{sub.title}</div>
                              <div className="text-sm text-gray-500">
                                Unit: <span className="font-medium text-blue-600">{sub.unit}</span>
                              </div>
                              <div className="text-sm text-gray-500">
                                Status: <span className={sub.status ? 'text-green-600' : 'text-red-600'}>
                                  {sub.status ? 'Active' : 'Inactive'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500">No subcategories found</div>
                  )}
                </div>
              ),
              rowExpandable: (record) => record.subcategories && record.subcategories.length > 0,
            }}
          />
        </div>
      </div>

      {/* Add/Edit Category Modal */}
      {openAddEditModal && (
        <AddEditCategoryModal
          open={openAddEditModal}
          onCancel={() => setOpenAddEditModal(false)}
          editData={editModalData}
          onSuccess={handleModalSuccess}
        />
      )}

      {/* Delete Confirmation Modal */}
      {openDeleteModal && (
        <DeleteConfirmModal
          title="Delete Category"
          description="Are you sure you want to delete this category? This action cannot be undone."
          open={openDeleteModal}
          onCancel={() => setOpenDeleteModal(false)}
          handleDelete={handleDeleteCategory}
          deleteLoading={deleteLoading}
        />
      )}
    </PageWrapper>
  );
};

export default Category;

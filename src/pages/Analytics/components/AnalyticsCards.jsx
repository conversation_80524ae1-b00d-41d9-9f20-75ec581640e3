import { Row, Col, Card, Statistic, Typography, Tag } from "antd";
import {
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PercentageOutlined,
  ClockCircleOutlined,
  BellOutlined,
  MobileOutlined,
} from "@ant-design/icons";
import { formatPercentage } from "../mockData";

const { Text } = Typography;

const MetricCard = ({ title, value, change, trend, period, icon, prefix, suffix }) => {
  const isPositive = trend === "up";
  const trendIcon = isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />;

  return (
    <Card className="h-full shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            {icon}
          </div>
          <div>
            <Text className="text-gray-600 text-sm">{title}</Text>
          </div>
        </div>
      </div>

      <div className="mb-3">
        <Statistic
          value={value}
          precision={typeof value === "number" && value % 1 !== 0 ? 1 : 0}
          prefix={prefix}
          suffix={suffix}
          valueStyle={{ fontSize: "28px", fontWeight: "600" }}
        />
      </div>

      <div className="flex items-center gap-2">
        <Tag
          color={isPositive ? "green" : "red"}
          icon={trendIcon}
          className="flex items-center gap-1"
        >
          {formatPercentage(Math.abs(change))}
        </Tag>
        <Text className="text-gray-500 text-xs">{period}</Text>
      </div>
    </Card>
  );
};

const AnalyticsCards = ({ data }) => {
  const { keyMetrics, appUsagePatterns, notificationEffectiveness } = data;

  const cardConfigs = [
    {
      title: "Total Users",
      value: keyMetrics.totalUsers.value,
      change: keyMetrics.totalUsers.change,
      trend: keyMetrics.totalUsers.trend,
      period: keyMetrics.totalUsers.period,
      icon: <UserOutlined className="text-blue-600 text-xl" />,
    },
    {
      title: "Total Revenue",
      value: keyMetrics.totalRevenue.value,
      change: keyMetrics.totalRevenue.change,
      trend: keyMetrics.totalRevenue.trend,
      period: keyMetrics.totalRevenue.period,
      icon: <DollarOutlined className="text-green-600 text-xl" />,
      prefix: "$",
    },
    {
      title: "Active Subscriptions",
      value: keyMetrics.activeSubscriptions.value,
      change: keyMetrics.activeSubscriptions.change,
      trend: keyMetrics.activeSubscriptions.trend,
      period: keyMetrics.activeSubscriptions.period,
      icon: <TrophyOutlined className="text-purple-600 text-xl" />,
    },
    {
      title: "Total Subscriptions",
      value: keyMetrics.totalSubscriptions.value,
      change: keyMetrics.totalSubscriptions.change,
      trend: keyMetrics.totalSubscriptions.trend,
      period: keyMetrics.totalSubscriptions.period,
      icon: <UserOutlined className="text-indigo-600 text-xl" />,
    },
    {
      title: "Total Items",
      value: keyMetrics.totalItems.value,
      change: keyMetrics.totalItems.change,
      trend: keyMetrics.totalItems.trend,
      period: keyMetrics.totalItems.period,
      icon: <ShoppingCartOutlined className="text-orange-600 text-xl" />,
    },
    {
      title: "Conversion Rate",
      value: keyMetrics.conversionRate.value,
      change: keyMetrics.conversionRate.change,
      trend: keyMetrics.conversionRate.trend,
      period: keyMetrics.conversionRate.period,
      icon: <PercentageOutlined className="text-pink-600 text-xl" />,
      suffix: "%",
    },
  ];

  // Calculate behavioral metrics
  const avgSessionDuration = appUsagePatterns?.sessionDuration?.reduce((acc, item) => {
    const midpoint = item.duration === "0-1 min" ? 0.5 :
      item.duration === "1-5 min" ? 3 :
        item.duration === "5-15 min" ? 10 :
          item.duration === "15-30 min" ? 22.5 : 35;
    return acc + (midpoint * item.percentage / 100);
  }, 0) || 8.5;

  const mobileUsagePercentage = appUsagePatterns?.deviceUsage?.find(d => d.device === "Mobile")?.percentage || 72.1;

  const avgNotificationOpenRate = notificationEffectiveness?.notificationTypes?.reduce((acc, notif) =>
    acc + notif.openRate, 0) / (notificationEffectiveness?.notificationTypes?.length || 1) || 69.0;

  const behavioralCards = [
    {
      title: "Avg Session Duration",
      value: avgSessionDuration,
      change: 5.2,
      trend: "up",
      period: "vs last month",
      icon: <ClockCircleOutlined className="text-cyan-600 text-xl" />,
      suffix: " min",
    },
    {
      title: "Mobile Usage",
      value: mobileUsagePercentage,
      change: 3.1,
      trend: "up",
      period: "vs last month",
      icon: <MobileOutlined className="text-emerald-600 text-xl" />,
      suffix: "%",
    },
    {
      title: "Notification Open Rate",
      value: avgNotificationOpenRate,
      change: 8.7,
      trend: "up",
      period: "vs last month",
      icon: <BellOutlined className="text-amber-600 text-xl" />,
      suffix: "%",
    },
  ];

  return (
    <div className="mb-8">
      {/* Main Analytics Cards */}
      <Row gutter={[16, 16]} className="mb-6">
        {cardConfigs.map((config, index) => (
          <Col xs={24} sm={12} lg={8} xl={4} key={index}>
            <MetricCard {...config} />
          </Col>
        ))}
      </Row>

      {/* Behavioral Analytics Cards */}
      {/* <div className="mb-4">
        <Text className="text-lg font-semibold text-gray-700">Behavioral Insights</Text>
      </div>
      <Row gutter={[16, 16]}>
        {behavioralCards.map((config, index) => (
          <Col xs={24} sm={12} lg={8} key={`behavioral-${index}`}>
            <MetricCard {...config} />
          </Col>
        ))}
      </Row> */}
    </div>
  );
};

export default AnalyticsCards;

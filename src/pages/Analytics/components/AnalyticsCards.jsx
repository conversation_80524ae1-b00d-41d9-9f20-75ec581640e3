import React from "react";
import { Row, Col, Card, Statistic, Typography, Tag } from "antd";
import {
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PercentageOutlined,
} from "@ant-design/icons";
import { formatCurrency, formatPercentage } from "../mockData";

const { Text } = Typography;

const MetricCard = ({ title, value, change, trend, period, icon, prefix, suffix }) => {
  const isPositive = trend === "up";
  const trendIcon = isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
  const trendColor = isPositive ? "#52c41a" : "#ff4d4f";

  return (
    <Card className="h-full shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            {icon}
          </div>
          <div>
            <Text className="text-gray-600 text-sm">{title}</Text>
          </div>
        </div>
      </div>

      <div className="mb-3">
        <Statistic
          value={value}
          precision={typeof value === "number" && value % 1 !== 0 ? 1 : 0}
          prefix={prefix}
          suffix={suffix}
          valueStyle={{ fontSize: "28px", fontWeight: "600" }}
        />
      </div>

      <div className="flex items-center gap-2">
        <Tag
          color={isPositive ? "green" : "red"}
          icon={trendIcon}
          className="flex items-center gap-1"
        >
          {formatPercentage(Math.abs(change))}
        </Tag>
        <Text className="text-gray-500 text-xs">{period}</Text>
      </div>
    </Card>
  );
};

const AnalyticsCards = ({ data }) => {
  const { keyMetrics } = data;

  const cardConfigs = [
    {
      title: "Total Users",
      value: keyMetrics.totalUsers.value,
      change: keyMetrics.totalUsers.change,
      trend: keyMetrics.totalUsers.trend,
      period: keyMetrics.totalUsers.period,
      icon: <UserOutlined className="text-blue-600 text-xl" />,
    },
    {
      title: "Total Revenue",
      value: keyMetrics.totalRevenue.value,
      change: keyMetrics.totalRevenue.change,
      trend: keyMetrics.totalRevenue.trend,
      period: keyMetrics.totalRevenue.period,
      icon: <DollarOutlined className="text-green-600 text-xl" />,
      prefix: "$",
    },
    {
      title: "Active Subscriptions",
      value: keyMetrics.activeSubscriptions.value,
      change: keyMetrics.activeSubscriptions.change,
      trend: keyMetrics.activeSubscriptions.trend,
      period: keyMetrics.activeSubscriptions.period,
      icon: <TrophyOutlined className="text-purple-600 text-xl" />,
    },
    {
      title: "Total Items",
      value: keyMetrics.totalItems.value,
      change: keyMetrics.totalItems.change,
      trend: keyMetrics.totalItems.trend,
      period: keyMetrics.totalItems.period,
      icon: <ShoppingCartOutlined className="text-orange-600 text-xl" />,
    },
    {
      title: "Conversion Rate",
      value: keyMetrics.conversionRate.value,
      change: keyMetrics.conversionRate.change,
      trend: keyMetrics.conversionRate.trend,
      period: keyMetrics.conversionRate.period,
      icon: <PercentageOutlined className="text-pink-600 text-xl" />,
      suffix: "%",
    },
  ];

  return (
    <div className="mb-8">
      <Row gutter={[16, 16]}>
        {cardConfigs.map((config, index) => (
          <Col xs={24} sm={12} lg={8} xl={4} key={index}>
            <MetricCard {...config} />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default AnalyticsCards;

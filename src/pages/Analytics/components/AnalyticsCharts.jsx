import { <PERSON>, <PERSON>, Card, Typography, List, Avatar } from "antd";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

const AnalyticsCharts = ({ data }) => {
  const {
    userGrowth,
    subscriptionPlan,
    weeklySubscriptions,
    monthlySubscriptions,
    topCategories,
    geographicData,
    recentActivities,
    appUsagePatterns,
    notificationEffectiveness,
  } = data;

  const getActivityIcon = (type) => {
    switch (type) {
      case "user_signup":
        return <UserOutlined className="text-blue-600" />;
      case "subscription":
        return <DollarOutlined className="text-green-600" />;
      case "item_added":
        return <ShoppingCartOutlined className="text-orange-600" />;
      default:
        return <TrophyOutlined className="text-purple-600" />;
    }
  };



  return (
    <div className="space-y-6">
      {/* User Growth and Revenue Charts */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="User Growth Trend" className="h-full">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={userGrowth}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="users" fill="#8884d8" name="Total Users" />
                <Bar dataKey="newUsers" fill="#82ca9d" name="New Users" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Monthly Subscription Revenue" className="h-full">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlySubscriptions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  name.includes('Revenue') ? `$${value.toLocaleString()}` : value,
                  name
                ]} />
                <Legend />
                <Bar dataKey="previousRevenue" fill="#e0e0e0" name="Previous Year Revenue" />
                <Bar dataKey="revenue" fill="#8884d8" name="Current Year Revenue" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Weekly Subscription Analysis */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="Weekly Subscription Purchases" className="h-full">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={weeklySubscriptions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="previousPurchases" fill="#e0e0e0" name="Previous Month" />
                <Bar dataKey="purchases" fill="#82ca9d" name="Current Month" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Weekly Subscription Revenue" className="h-full">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={weeklySubscriptions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  `$${value.toLocaleString()}`,
                  name
                ]} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="previousRevenue"
                  stroke="#e0e0e0"
                  strokeWidth={2}
                  name="Previous Month Revenue"
                  strokeDasharray="5 5"
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#8884d8"
                  strokeWidth={3}
                  name="Current Month Revenue"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Subscription Plan and Categories */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card title="Subscription Plan Details" className="h-full">
            <div className="p-4">
              <div className="text-center mb-4">
                <Title level={4} className="!mb-2">{subscriptionPlan.name}</Title>
                <Text className="text-gray-600">Single Premium Plan</Text>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <Text className="font-medium">Total Subscribers</Text>
                  <Text className="text-xl font-bold text-blue-600">
                    {subscriptionPlan.totalUsers.toLocaleString()}
                  </Text>
                </div>

                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <Text className="font-medium">Monthly Price</Text>
                  <Text className="text-lg font-semibold text-green-600">
                    ${subscriptionPlan.monthlyPrice}
                  </Text>
                </div>

                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <Text className="font-medium">Yearly Price</Text>
                  <Text className="text-lg font-semibold text-purple-600">
                    ${subscriptionPlan.yearlyPrice}
                  </Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Top Categories" className="h-full">
            <div className="space-y-3">
              {topCategories.map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <Text className="font-medium">{category.category}</Text>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <Text className="font-semibold">{category.items}</Text>
                    <br />
                    <Text className="text-gray-500 text-xs">{category.percentage}%</Text>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Recent Activities" className="h-full">
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item className="!px-0">
                  <List.Item.Meta
                    avatar={<Avatar icon={getActivityIcon(item.type)} />}
                    title={
                      <Text className="text-sm font-medium">{item.message}</Text>
                    }
                    description={
                      <Text className="text-xs text-gray-500">{item.timestamp}</Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* User Activity and Geographic Distribution */}
      <Row gutter={[16, 16]}>
        {/* <Col xs={24} lg={12}>
          <Card title="Weekly User Activity" className="h-full">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="active"
                  stroke="#8884d8"
                  strokeWidth={2}
                  name="Active Users"
                />
                <Line
                  type="monotone"
                  dataKey="logins"
                  stroke="#82ca9d"
                  strokeWidth={2}
                  name="Daily Logins"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col> */}

        <Col xs={24} lg={12}>
          <Card title="Geographic Distribution" className="h-full">
            <div className="space-y-4">
              {geographicData.map((region, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <Text className="font-medium">{region.region}</Text>
                      <Text className="text-gray-500">{region.percentage}%</Text>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                        style={{ width: `${region.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <Text className="font-semibold">{region.users.toLocaleString()}</Text>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Behavioral Analytics Section
      <div className="mt-8">
        <div className="mb-6">
          <Title level={3} className="!mb-2">
            Behavioral Analytics
          </Title>
          <Text className="text-gray-600">
            App usage patterns and user engagement insights
          </Text>
        </div>

        App Usage Patterns
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} lg={12}>
            <Card title="Hourly App Usage" className="h-full">
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={appUsagePatterns.hourlyUsage}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="users"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                    name="Active Users"
                  />
                  <Area
                    type="monotone"
                    dataKey="sessions"
                    stackId="2"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    fillOpacity={0.6}
                    name="Sessions"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="Device Usage Distribution" className="h-full">
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={appUsagePatterns.deviceUsage}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ device, percentage }) => `${device}: ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="users"
                  >
                    {appUsagePatterns.deviceUsage.map((entry, index) => (
                      <Cell key={`device-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        Feature Usage and Session Duration
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} lg={12}>
            <Card title="Feature Usage" className="h-full">
              <div className="space-y-3">
                {appUsagePatterns.featureUsage.map((feature, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <Text className="font-medium">{feature.feature}</Text>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${feature.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-4 text-right">
                      <Text className="font-semibold">{feature.usage.toLocaleString()}</Text>
                      <br />
                      <Text className="text-gray-500 text-xs">{feature.percentage}%</Text>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="Session Duration" className="h-full">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={appUsagePatterns.sessionDuration}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="duration" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="users" fill="#8884d8" name="Users" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        Notification Effectiveness
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card title="Notification Performance" className="h-full">
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={notificationEffectiveness.notificationTypes}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="type" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="openRate" fill="#8884d8" name="Open Rate %" />
                  <Bar dataKey="clickRate" fill="#82ca9d" name="Click Rate %" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="Notification Timing" className="h-full">
              <ResponsiveContainer width="100%" height={350}>
                <LineChart data={notificationEffectiveness.timingEffectiveness}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="openRate"
                    stroke="#8884d8"
                    strokeWidth={2}
                    name="Open Rate %"
                  />
                  <Line
                    type="monotone"
                    dataKey="clickRate"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    name="Click Rate %"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </div> */}
    </div>
  );
};

export default AnalyticsCharts;

// Mock data for analytics dashboard

export const analyticsData = {
  // Key metrics cards
  keyMetrics: {
    totalUsers: {
      value: 12847,
      change: 12.5,
      trend: "up",
      period: "vs last month"
    },
    totalRevenue: {
      value: 89650,
      change: 8.2,
      trend: "up",
      period: "vs last month"
    },
    activeSubscriptions: {
      value: 3421,
      change: 15.3,
      trend: "up",
      period: "vs last month"
    },
    totalSubscriptions: {
      value: 4850,
      change: 8.3,
      trend: "up",
      period: "vs last month"
    },
    totalItems: {
      value: 5678,
      change: 5.7,
      trend: "up",
      period: "vs last month"
    },
    averageOrderValue: {
      value: 26.2,
      change: -2.1,
      trend: "down",
      period: "vs last month"
    },
    conversionRate: {
      value: 3.8,
      change: 0.5,
      trend: "up",
      period: "vs last month"
    },
    dailyActiveUsers: {
      value: 8420,
      change: 7.3,
      trend: "up",
      period: "vs last month"
    },
    avgSessionDuration: {
      value: 8.5,
      change: 5.2,
      trend: "up",
      period: "vs last month"
    }
  },

  // User growth data for charts
  userGrowth: [
    { month: "Jan", users: 8500, newUsers: 850 },
    { month: "Feb", users: 9200, newUsers: 920 },
    { month: "Mar", users: 9800, newUsers: 980 },
    { month: "Apr", users: 10500, newUsers: 1050 },
    { month: "May", users: 11200, newUsers: 1120 },
    { month: "Jun", users: 12847, newUsers: 1284 }
  ],

  // Revenue data for charts (subscription-based only)
  revenueData: [
    { month: "Jan", revenue: 65000, subscriptions: 65000 },
    { month: "Feb", revenue: 72000, subscriptions: 72000 },
    { month: "Mar", revenue: 68000, subscriptions: 68000 },
    { month: "Apr", revenue: 78000, subscriptions: 78000 },
    { month: "May", revenue: 82000, subscriptions: 82000 },
    { month: "Jun", revenue: 89650, subscriptions: 89650 }
  ],

  // Single subscription plan data
  subscriptionPlan: {
    name: "PantryPal Premium",
    totalUsers: 3421,
    monthlyPrice: 9.99,
    yearlyPrice: 99.99
  },

  // Weekly subscription purchases and revenue
  weeklySubscriptions: [
    { week: "Week 1", purchases: 45, revenue: 449.55, previousPurchases: 38, previousRevenue: 379.62 },
    { week: "Week 2", purchases: 52, revenue: 519.48, previousPurchases: 41, previousRevenue: 409.59 },
    { week: "Week 3", purchases: 48, revenue: 479.52, previousPurchases: 44, previousRevenue: 439.56 },
    { week: "Week 4", purchases: 61, revenue: 609.39, previousPurchases: 49, previousRevenue: 489.51 }
  ],

  // Monthly subscription trends
  monthlySubscriptions: [
    { month: "Jan", purchases: 180, revenue: 1798.20, previousPurchases: 165, previousRevenue: 1648.35 },
    { month: "Feb", purchases: 195, revenue: 1948.05, previousPurchases: 172, previousRevenue: 1718.28 },
    { month: "Mar", purchases: 210, revenue: 2097.90, previousPurchases: 188, previousRevenue: 1878.12 },
    { month: "Apr", purchases: 225, revenue: 2247.75, previousPurchases: 201, previousRevenue: 2007.99 },
    { month: "May", purchases: 240, revenue: 2397.60, previousPurchases: 218, previousRevenue: 2177.82 },
    { month: "Jun", purchases: 265, revenue: 2647.35, previousPurchases: 235, previousRevenue: 2347.65 }
  ],

  // Yearly subscription trends
  yearlySubscriptions: [
    { year: "2020", purchases: 1800, revenue: 17982.00, previousPurchases: 1200, previousRevenue: 11988.00 },
    { year: "2021", purchases: 2400, revenue: 23976.00, previousPurchases: 1800, previousRevenue: 17982.00 },
    { year: "2022", purchases: 2850, revenue: 28471.50, previousPurchases: 2400, previousRevenue: 23976.00 },
    { year: "2023", purchases: 3200, revenue: 31968.00, previousPurchases: 2850, previousRevenue: 28471.50 },
    { year: "2024", purchases: 3650, revenue: 36463.50, previousPurchases: 3200, previousRevenue: 31968.00 }
  ],

  // Top categories by items
  topCategories: [
    { category: "Fruits & Vegetables", items: 1245, percentage: 21.9 },
    { category: "Dairy & Eggs", items: 987, percentage: 17.4 },
    { category: "Meat & Seafood", items: 856, percentage: 15.1 },
    { category: "Pantry Staples", items: 743, percentage: 13.1 },
    { category: "Beverages", items: 654, percentage: 11.5 },
    { category: "Snacks", items: 567, percentage: 10.0 },
    { category: "Others", items: 626, percentage: 11.0 }
  ],

  // User activity data
  userActivity: [
    { day: "Mon", active: 2400, logins: 1800 },
    { day: "Tue", active: 2200, logins: 1650 },
    { day: "Wed", active: 2800, logins: 2100 },
    { day: "Thu", active: 2600, logins: 1950 },
    { day: "Fri", active: 3200, logins: 2400 },
    { day: "Sat", active: 2900, logins: 2175 },
    { day: "Sun", active: 2100, logins: 1575 }
  ],

  // Geographic distribution
  geographicData: [
    { region: "North America", users: 4500, percentage: 35.0 },
    { region: "Europe", users: 3200, percentage: 24.9 },
    { region: "Asia Pacific", users: 2800, percentage: 21.8 },
    { region: "Latin America", users: 1500, percentage: 11.7 },
    { region: "Others", users: 847, percentage: 6.6 }
  ],

  // Recent activities
  recentActivities: [
    {
      id: 1,
      type: "user_signup",
      message: "New user registered: <EMAIL>",
      timestamp: "2 minutes ago",
      icon: "user"
    },
    {
      id: 2,
      type: "subscription",
      message: "Premium subscription purchased by Sarah Wilson",
      timestamp: "15 minutes ago",
      icon: "dollar"
    },
    {
      id: 3,
      type: "item_added",
      message: "New item added: Organic Bananas",
      timestamp: "1 hour ago",
      icon: "shopping"
    },
    {
      id: 4,
      type: "user_signup",
      message: "New user registered: <EMAIL>",
      timestamp: "2 hours ago",
      icon: "user"
    },
    {
      id: 5,
      type: "subscription",
      message: "Enterprise plan upgraded by TechCorp Inc.",
      timestamp: "3 hours ago",
      icon: "trophy"
    }
  ],

  // Behavioral Analytics - App Usage Patterns
  appUsagePatterns: {
    // Hourly usage throughout the day
    hourlyUsage: [
      { hour: "00", users: 120, sessions: 145 },
      { hour: "01", users: 85, sessions: 98 },
      { hour: "02", users: 65, sessions: 72 },
      { hour: "03", users: 45, sessions: 52 },
      { hour: "04", users: 38, sessions: 43 },
      { hour: "05", users: 52, sessions: 61 },
      { hour: "06", users: 180, sessions: 220 },
      { hour: "07", users: 420, sessions: 520 },
      { hour: "08", users: 680, sessions: 850 },
      { hour: "09", users: 520, sessions: 640 },
      { hour: "10", users: 380, sessions: 460 },
      { hour: "11", users: 450, sessions: 550 },
      { hour: "12", users: 620, sessions: 780 },
      { hour: "13", users: 580, sessions: 720 },
      { hour: "14", users: 420, sessions: 510 },
      { hour: "15", users: 380, sessions: 460 },
      { hour: "16", users: 450, sessions: 550 },
      { hour: "17", users: 680, sessions: 850 },
      { hour: "18", users: 820, sessions: 1020 },
      { hour: "19", users: 920, sessions: 1150 },
      { hour: "20", users: 780, sessions: 980 },
      { hour: "21", users: 620, sessions: 780 },
      { hour: "22", users: 420, sessions: 520 },
      { hour: "23", users: 280, sessions: 340 }
    ],

    // Feature usage statistics
    featureUsage: [
      { feature: "Pantry Inventory", usage: 8500, percentage: 85.2 },
      { feature: "Shopping Lists", usage: 7200, percentage: 72.1 },
      { feature: "Expiry Alerts", usage: 6800, percentage: 68.3 },
      { feature: "Recipe Suggestions", usage: 5400, percentage: 54.1 },
      { feature: "Meal Planning", usage: 4200, percentage: 42.1 },
      { feature: "Nutrition Tracking", usage: 3800, percentage: 38.1 },
      { feature: "Barcode Scanner", usage: 3200, percentage: 32.1 },
      { feature: "Price Tracking", usage: 2800, percentage: 28.1 }
    ],

    // Session duration distribution
    sessionDuration: [
      { duration: "0-1 min", users: 1200, percentage: 12.1 },
      { duration: "1-5 min", users: 3400, percentage: 34.2 },
      { duration: "5-15 min", users: 2800, percentage: 28.1 },
      { duration: "15-30 min", users: 1600, percentage: 16.1 },
      { duration: "30+ min", users: 950, percentage: 9.5 }
    ],

    // Device usage breakdown
    deviceUsage: [
      { device: "Mobile", users: 7200, percentage: 72.1, color: "#8884d8" },
      { device: "Desktop", users: 1800, percentage: 18.1, color: "#82ca9d" },
      { device: "Tablet", users: 980, percentage: 9.8, color: "#ffc658" }
    ]
  },

  // Notification Effectiveness
  notificationEffectiveness: {
    // Notification types and their performance
    notificationTypes: [
      {
        type: "Expiry Alerts",
        sent: 15420,
        opened: 12336,
        clicked: 9869,
        openRate: 80.0,
        clickRate: 64.0,
        actionTaken: 7895
      },
      {
        type: "Shopping Reminders",
        sent: 12800,
        opened: 8960,
        clicked: 6400,
        openRate: 70.0,
        clickRate: 50.0,
        actionTaken: 5120
      },
      {
        type: "Recipe Suggestions",
        sent: 9600,
        opened: 5760,
        clicked: 3840,
        openRate: 60.0,
        clickRate: 40.0,
        actionTaken: 2880
      },
      {
        type: "Price Drops",
        sent: 8400,
        opened: 5880,
        clicked: 4200,
        openRate: 70.0,
        clickRate: 50.0,
        actionTaken: 3360
      },
      {
        type: "Weekly Summary",
        sent: 10000,
        opened: 6500,
        clicked: 3000,
        openRate: 65.0,
        clickRate: 30.0,
        actionTaken: 2100
      }
    ],

    // Notification timing effectiveness
    timingEffectiveness: [
      { time: "6 AM", openRate: 45.2, clickRate: 28.1 },
      { time: "8 AM", openRate: 72.5, clickRate: 45.3 },
      { time: "12 PM", openRate: 68.1, clickRate: 42.1 },
      { time: "3 PM", openRate: 52.3, clickRate: 31.2 },
      { time: "6 PM", openRate: 78.9, clickRate: 52.1 },
      { time: "8 PM", openRate: 65.4, clickRate: 38.7 },
      { time: "10 PM", openRate: 42.1, clickRate: 22.3 }
    ],

    // User engagement after notifications
    engagementMetrics: {
      averageSessionAfterNotification: 8.5, // minutes
      itemsAddedAfterAlert: 2.3,
      shoppingListsCreated: 1.8,
      recipesViewed: 3.2
    }
  }
};

// Helper function to generate random data for testing
export const generateRandomData = (baseValue, variance = 0.1) => {
  const randomFactor = 1 + (Math.random() - 0.5) * variance;
  return Math.round(baseValue * randomFactor);
};

// Helper function to format currency
export const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

// Helper function to format percentage
export const formatPercentage = (value) => {
  return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};

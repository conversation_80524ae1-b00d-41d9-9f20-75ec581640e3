// Mock data for analytics dashboard

export const analyticsData = {
  // Key metrics cards
  keyMetrics: {
    totalUsers: {
      value: 12847,
      change: 12.5,
      trend: "up",
      period: "vs last month"
    },
    totalRevenue: {
      value: 89650,
      change: 8.2,
      trend: "up",
      period: "vs last month"
    },
    activeSubscriptions: {
      value: 3421,
      change: 15.3,
      trend: "up",
      period: "vs last month"
    },
    totalItems: {
      value: 5678,
      change: 5.7,
      trend: "up",
      period: "vs last month"
    },
    averageOrderValue: {
      value: 26.2,
      change: -2.1,
      trend: "down",
      period: "vs last month"
    },
    conversionRate: {
      value: 3.8,
      change: 0.5,
      trend: "up",
      period: "vs last month"
    }
  },

  // User growth data for charts
  userGrowth: [
    { month: "Jan", users: 8500, newUsers: 850 },
    { month: "Feb", users: 9200, newUsers: 920 },
    { month: "Mar", users: 9800, newUsers: 980 },
    { month: "Apr", users: 10500, newUsers: 1050 },
    { month: "May", users: 11200, newUsers: 1120 },
    { month: "Jun", users: 12847, newUsers: 1284 }
  ],

  // Revenue data for charts
  revenueData: [
    { month: "Jan", revenue: 65000, subscriptions: 45000, oneTime: 20000 },
    { month: "Feb", revenue: 72000, subscriptions: 48000, oneTime: 24000 },
    { month: "Mar", revenue: 68000, subscriptions: 46000, oneTime: 22000 },
    { month: "Apr", revenue: 78000, subscriptions: 52000, oneTime: 26000 },
    { month: "May", revenue: 82000, subscriptions: 55000, oneTime: 27000 },
    { month: "Jun", revenue: 89650, subscriptions: 59650, oneTime: 30000 }
  ],

  // Subscription plans distribution
  subscriptionPlans: [
    { name: "Basic", users: 1200, percentage: 35.1, color: "#8884d8" },
    { name: "Premium", users: 1500, percentage: 43.9, color: "#82ca9d" },
    { name: "Enterprise", users: 721, percentage: 21.0, color: "#ffc658" }
  ],

  // Top categories by items
  topCategories: [
    { category: "Fruits & Vegetables", items: 1245, percentage: 21.9 },
    { category: "Dairy & Eggs", items: 987, percentage: 17.4 },
    { category: "Meat & Seafood", items: 856, percentage: 15.1 },
    { category: "Pantry Staples", items: 743, percentage: 13.1 },
    { category: "Beverages", items: 654, percentage: 11.5 },
    { category: "Snacks", items: 567, percentage: 10.0 },
    { category: "Others", items: 626, percentage: 11.0 }
  ],

  // User activity data
  userActivity: [
    { day: "Mon", active: 2400, logins: 1800 },
    { day: "Tue", active: 2200, logins: 1650 },
    { day: "Wed", active: 2800, logins: 2100 },
    { day: "Thu", active: 2600, logins: 1950 },
    { day: "Fri", active: 3200, logins: 2400 },
    { day: "Sat", active: 2900, logins: 2175 },
    { day: "Sun", active: 2100, logins: 1575 }
  ],

  // Geographic distribution
  geographicData: [
    { region: "North America", users: 4500, percentage: 35.0 },
    { region: "Europe", users: 3200, percentage: 24.9 },
    { region: "Asia Pacific", users: 2800, percentage: 21.8 },
    { region: "Latin America", users: 1500, percentage: 11.7 },
    { region: "Others", users: 847, percentage: 6.6 }
  ],

  // Recent activities
  recentActivities: [
    {
      id: 1,
      type: "user_signup",
      message: "New user registered: <EMAIL>",
      timestamp: "2 minutes ago",
      icon: "user"
    },
    {
      id: 2,
      type: "subscription",
      message: "Premium subscription purchased by Sarah Wilson",
      timestamp: "15 minutes ago",
      icon: "dollar"
    },
    {
      id: 3,
      type: "item_added",
      message: "New item added: Organic Bananas",
      timestamp: "1 hour ago",
      icon: "shopping"
    },
    {
      id: 4,
      type: "user_signup",
      message: "New user registered: <EMAIL>",
      timestamp: "2 hours ago",
      icon: "user"
    },
    {
      id: 5,
      type: "subscription",
      message: "Enterprise plan upgraded by TechCorp Inc.",
      timestamp: "3 hours ago",
      icon: "trophy"
    }
  ]
};

// Helper function to generate random data for testing
export const generateRandomData = (baseValue, variance = 0.1) => {
  const randomFactor = 1 + (Math.random() - 0.5) * variance;
  return Math.round(baseValue * randomFactor);
};

// Helper function to format currency
export const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

// Helper function to format percentage
export const formatPercentage = (value) => {
  return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};

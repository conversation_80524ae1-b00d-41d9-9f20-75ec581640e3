import React from "react";
import { Row, Col, Card, Statistic, Typography } from "antd";
import {
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
} from "@ant-design/icons";

import AnalyticsCards from "./components/AnalyticsCards";
import AnalyticsCharts from "./components/AnalyticsCharts";
import { analyticsData } from "./mockData";

const { Title } = Typography;

const Analytics = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2} className="!mb-2">
          Analytics & Reports
        </Title>
        <p className="text-gray-600">
          Monitor your platform's performance and key metrics
        </p>
      </div>

      {/* Analytics Cards */}
      <AnalyticsCards data={analyticsData} />

      {/* Analytics Charts */}
      <AnalyticsCharts data={analyticsData} />
    </div>
  );
};

export default Analytics;

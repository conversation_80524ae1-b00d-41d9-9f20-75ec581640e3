import React, { useState, useEffect } from "react";
import { Button, Tag, Modal, Form, Input, Select, message } from "antd";
import { BellOutlined, SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";

import PageWrapper from "../../components/common/PageWrapper";
import PageTitle from "../../components/common/PageTitle";
import CustomTable from "../../components/common/CustomTable";
import CustomInput from "../../components/form/CustomInput";
import DeleteConfirmModal from "../../components/common/DeleteConfirmModal";
import {
  getNotificationTemplates,
  createNotificationTemplate,
  updateNotificationTemplate,
  deleteNotificationTemplate,
  templateTypes
} from "../../services/notificationTemplates";

const NotificationManagement = () => {
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [searchInputValue, setSearchInputValue] = useState("");
  const [addEditModalOpen, setAddEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [form] = Form.useForm();

  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    searchTerm: "",
  });



  // Table columns
  const columns = [
    {
      title: "Template Type",
      dataIndex: "type",
      key: "type",
      width: 200,
      render: (type) => {
        const templateType = templateTypes.find(t => t.value === type);
        return (
          <Tag color="blue">
            {templateType ? templateType.label : type}
          </Tag>
        );
      },
      sorter: true,
    },
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      width: 200,
      render: (text) => <span className="font-medium">{text}</span>,
      sorter: true,
    },
    {
      title: "Subject",
      dataIndex: "subject",
      key: "subject",
      width: 300,
      render: (text) => (
        <div className="truncate max-w-xs" title={text}>
          {text}
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      width: 100,
      align: "center",
      render: (isActive) => (
        <Tag color={isActive ? "green" : "red"}>
          {isActive ? "Active" : "Inactive"}
        </Tag>
      ),
      sorter: true,
    },
    {
      title: "Created Date",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: "Actions",
      key: "actions",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="flex gap-2 justify-center">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className="text-blue-600 hover:text-blue-800"
          />
          <Button
            type="text"
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            className="text-red-600 hover:text-red-800"
          />
        </div>
      ),
    },
  ];

  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchInputValue(value);
    setTableParams({
      ...tableParams,
      searchTerm: value,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleClearSearch = () => {
    setSearchInputValue("");
    setTableParams({
      ...tableParams,
      searchTerm: "",
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  const handleAdd = () => {
    setEditingTemplate(null);
    form.resetFields();
    setAddEditModalOpen(true);
  };

  const handleEdit = (template) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      type: template.type,
      title: template.title,
      subject: template.subject,
      message: template.message,
      isActive: template.isActive,
    });
    setAddEditModalOpen(true);
  };

  const handleDelete = (template) => {
    setSelectedTemplate(template);
    setDeleteModalOpen(true);
  };

  const handleSubmit = async (values) => {
    try {
      setSaving(true);

      if (editingTemplate) {
        await updateNotificationTemplate(editingTemplate.id, values);
        message.success(`Template "${values.title}" updated successfully!`);
      } else {
        await createNotificationTemplate(values);
        message.success(`Template "${values.title}" created successfully!`);
      }

      setAddEditModalOpen(false);
      form.resetFields();
      setEditingTemplate(null);
      setSaving(false);

      // Refresh data
      getNotificationTemplatesData();

    } catch (error) {
      setSaving(false);
      message.error("Failed to save template. Please try again.");
    }
  };

  const handleConfirmDelete = async () => {
    try {
      setDeleting(true);

      await deleteNotificationTemplate(selectedTemplate.id);
      message.success(`Template "${selectedTemplate.title}" deleted successfully!`);
      setDeleteModalOpen(false);
      setSelectedTemplate(null);
      setDeleting(false);

      // Refresh data
      getNotificationTemplatesData();

    } catch (error) {
      setDeleting(false);
      message.error("Failed to delete template. Please try again.");
    }
  };

  const getNotificationTemplatesData = async (searchTerm = "") => {
    try {
      setTableLoading(true);

      const templates = await getNotificationTemplates();
      let filteredData = [...templates];

      // Apply search filter
      if (searchTerm) {
        filteredData = filteredData.filter(template =>
          template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
          template.type.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      setTableData(filteredData);

      // Update pagination total separately to avoid infinite loop
      setTableParams(prev => ({
        ...prev,
        pagination: {
          ...prev.pagination,
          total: filteredData.length,
        },
      }));

      setTableLoading(false);

    } catch (error) {
      setTableLoading(false);
      message.error("Failed to load notification templates.");
    }
  };

  useEffect(() => {
    getNotificationTemplatesData(tableParams.searchTerm);
  }, [tableParams.searchTerm]);

  return (
    <PageWrapper>
      <div className="h-full flex flex-col" style={{ minHeight: '80vh' }}>
        <div className="flex justify-between items-center">
          <PageTitle
            title="Notification Management"
            icon={<BellOutlined style={{ backgroundColor: "#2D5A3D", color: "white", borderRadius: "4px", padding: "4px" }} />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            Add Template
          </Button>
        </div>

        <div className="pt-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <CustomInput
              placeholder={"Search by title, subject, or type"}
              prefix={<SearchOutlined className="text-[#d9d9d9]" />}
              value={searchInputValue}
              onChange={handleSearchInputChange}
              allowClear
              onClear={handleClearSearch}
            />
          </div>
          <div className="text-sm text-gray-500">
            {tableData.length} template(s) found
          </div>
        </div>

        <div className="flex-1 overflow-hidden pt-4" style={{ minHeight: '500px' }}>
          <CustomTable
            scrollX={1000}
            rowKey={(record) => record?.id}
            columns={columns}
            dataSource={tableData}
            loading={tableLoading}
            onChange={(pagination, filters, sorter) => {
              setTableParams({
                ...tableParams,
                pagination,
                ...filters,
                ...sorter,
              });
            }}
            pagination={tableParams?.pagination}
          />
        </div>
      </div>

      {/* Add/Edit Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <BellOutlined />
            <span>{editingTemplate ? "Edit Template" : "Add Template"}</span>
          </div>
        }
        open={addEditModalOpen}
        onCancel={() => {
          setAddEditModalOpen(false);
          form.resetFields();
          setEditingTemplate(null);
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ isActive: true }}
        >
          <Form.Item
            label="Template Type"
            name="type"
            rules={[{ required: true, message: 'Please select a template type' }]}
          >
            <Select
              placeholder="Select template type"
              options={templateTypes}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            label="Title"
            name="title"
            rules={[{ required: true, message: 'Please enter a title' }]}
          >
            <Input placeholder="Enter template title" />
          </Form.Item>

          <Form.Item
            label="Subject"
            name="subject"
            rules={[{ required: true, message: 'Please enter a subject' }]}
          >
            <Input placeholder="Enter email subject" />
          </Form.Item>

          <Form.Item
            label="Message"
            name="message"
            rules={[{ required: true, message: 'Please enter a message' }]}
          >
            <Input.TextArea
              rows={6}
              placeholder="Enter notification message"
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item
            label="Status"
            name="isActive"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select
              placeholder="Select status"
              options={[
                { label: "Active", value: true },
                { label: "Inactive", value: false },
              ]}
            />
          </Form.Item>

          <div className="text-xs text-gray-500 mb-4">
            <strong>Available placeholders:</strong> {"{userName}"}, {"{expiryDate}"}, {"{planName}"}, {"{amount}"}
          </div>

          <div className="flex justify-end gap-2">
            <Button
              onClick={() => {
                setAddEditModalOpen(false);
                form.resetFields();
                setEditingTemplate(null);
              }}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={saving}
            >
              {editingTemplate ? "Update Template" : "Create Template"}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <DeleteConfirmModal
          title="Delete Notification Template"
          description={`Are you sure you want to delete the template "${selectedTemplate?.title}"? This action cannot be undone.`}
          open={deleteModalOpen}
          onCancel={() => {
            setDeleteModalOpen(false);
            setSelectedTemplate(null);
          }}
          handleDelete={handleConfirmDelete}
          deleteLoading={deleting}
        />
      )}
    </PageWrapper>
  );
};

export default NotificationManagement;

import { Button, Modal } from "antd";
import React from "react";

const StatusChangeConfirmModal = ({
  open,
  onCancel,
  currentStatus,
  handleStatusChange,
  loading,
  title,
  message,
}) => {
  return (
    <Modal
      title={title}
      open={open}
      confirmLoading={loading}
      maskClosable={false}
      closable={false}
      footer={null}
    >
      <p>{message}</p>
      <div className="flex justify-end gap-2 pt-3">
        <Button onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="primary" onClick={handleStatusChange} loading={loading}>
          {!currentStatus ? "Suspend" : "Reactivate"}
        </Button>
      </div>
    </Modal>
  );
};

export default StatusChangeConfirmModal;

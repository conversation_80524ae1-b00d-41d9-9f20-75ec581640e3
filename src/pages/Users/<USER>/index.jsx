import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";

import Api from "../../../utils/apiHandler";
import { showToast } from "../../../utils/showToast";
import { showError } from "../../../utils/showError";
import { userSchema } from "../../../utils/validationSchema";
import { ApiRoute } from "../../../config/apiEndpoint";
import CustomInput from "../../../components/form/CustomInput";
import CustomMobileNoInput from "../../../components/form/CustomMobileNoInput";

const EditUserModal = ({ open, onCancel, editUserModalData, onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleFormSubmit = async (values) => {
    // try {
    //   setIsLoading(true);
    //   const res = await Api("POST", ApiRoute.userEdit, {
    //     ...values,
    //     id: editUserModalData?.id,
    //   });
    //   if (res?.data?.status) {
    //     await onSuccess();
    onCancel();
    //     showToast.success(res?.data?.message);
    //   } else {
    //     showToast.error(res?.data?.message);
    //   }
    //   setIsLoading(false);
    // } catch (err) {
    //   setIsLoading(false);
    //   showError(err);
    // }
  };

  const {
    values,
    handleChange,
    handleBlur,
    handleSubmit,
    errors,
    touched,
    setValues,
    setFieldValue,
  } = useFormik({
    initialValues: {
      id: 0,
      fullName: "",
      email: "",
      countryCode: "+91",
      mobileNumber: "",
    },
    validationSchema: userSchema,
    onSubmit: handleFormSubmit,
  });

  useEffect(() => {
    if (editUserModalData) {
      setValues({
        id: editUserModalData?.id,
        fullName: editUserModalData?.fullName,
        email: editUserModalData?.email,
        countryCode: editUserModalData?.countryCode ?? "+91",
        mobileNumber: editUserModalData?.mobileNumber,
        // emergencyNumber: editUserModalData?.emergencyNumber,
        // emergencyNumberCountryCode:
        //   editUserModalData?.emergencyNumberCountryCode ?? "+91",
        // bloodGroup: editUserModalData?.bloodGroup,
      });
    }
  }, [editUserModalData]);
  return (
    <Modal
      title={"Update User Details"}
      open={open}
      onCancel={onCancel}
      footer={null}
      width={"auto"}
      centered
      style={{
        minWidth: "360px",
        maxWidth: "460px",
      }}
    >
      <form onSubmit={handleSubmit}>
        <div className="pb-4">
          <CustomInput
            label={"Full Name"}
            name={"fullName"}
            placeholder={"Enter Full Name"}
            value={values?.fullName}
            onChange={handleChange}
            onBlur={handleBlur}
            errors={errors}
            touched={touched}
            showRequired
          />
        </div>
        <div className="pb-4">
          <CustomInput
            label={"Email"}
            name={"email"}
            placeholder={"Enter email"}
            value={values?.email}
            onChange={handleChange}
            onBlur={handleBlur}
            errors={errors}
            touched={touched}
            disabled={true}
          />
        </div>
        {/* <Divider plain>or</Divider> */}
        <div className="pb-4">
          <CustomMobileNoInput
            label={"Mobile Number"}
            name={"mobileNumber"}
            placeholder={"Enter mobile number"}
            mobileNoValue={values?.mobileNumber}
            onMobileNoChange={handleChange}
            onMobileNoBlur={handleBlur}
            countryCodeValue={values?.countryCode}
            onCountryChange={(value) => setFieldValue("countryCode", value)}
            errors={errors}
            touched={touched}
            disabled={true}
          />
        </div>

        <Button type="primary" htmlType="submit" loading={isLoading}>
          Submit
        </Button>
      </form>
    </Modal>
  );
};

export default EditUserModal;

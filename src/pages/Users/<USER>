import { UserOutlined } from "@ant-design/icons";
import { Avatar, Switch } from "antd";

import {
  DeleteButton,
  EditButton,
  ViewButton,
} from "../../components/common/TableButtons";
import { browserRoutes } from "../../config/browserRoutes";

export const getColumns = ({
  tableParams,
  handleSorterHeaderCellClick,
  setOpenStatusChangeModal,
  setStatusChangeData,
  setOpenDeleteModal,
  setDeletedRecordId,
  setEditUserModalData,
  setOpenEditUserModal,
  navigate,
}) => {
  const registeredUserColumns = [
    {
      title: <span className="w-full flex justify-center">Profile</span>,
      dataIndex: "avatar",
      key: "avatar",
      width: 88,
      render: (value, record) => {
        return (
          <div className="w-full flex justify-center">
            {value ? (
              <Avatar className="w-14 h-14" src={value} />
            ) : (
              <Avatar className="w-14 h-14" icon={<UserOutlined />} />
            )}
          </div>
        );
      },
    },
    {
      title: "Full Name",
      dataIndex: "fullName",
      key: "fullName",
      align: "center",
      width: 250,
      render: (value, record) => {
        return <div className="truncate">{value ? value : "-"}</div>;
      },
      sorter: () => true,
      sortOrder:
        tableParams?.columnKey === "fullName" ? tableParams?.order : null,
      onHeaderCell: (column) => {
        return {
          onClick: () => handleSorterHeaderCellClick(column),
        };
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      align: "center",
      width: 300,
      render: (value, record) => {
        return <div className="truncate">{value ? value : "-"}</div>;
      },
      sorter: () => true,
      sortOrder: tableParams?.columnKey === "email" ? tableParams?.order : null,
      onHeaderCell: (column) => {
        return {
          onClick: () => handleSorterHeaderCellClick(column),
        };
      },
    },
    {
      title: "Mobile number",
      dataIndex: "mobileNumber",
      key: "mobileNumber",
      align: "center",
      width: 200,
      render: (_, record) => {
        return (
          <div className="truncate">
            {record?.mobileNumber
              ? record?.countryCode + " " + record?.mobileNumber
              : "-"}
          </div>
        );
      },
    },
    {
      title: <span className="w-full flex justify-center">Status</span>,
      key: "status",
      dataIndex: "status",
      width: 78,
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center">
            <Switch
              size="small"
              value={record?.status}
              onChange={() => {
                setOpenStatusChangeModal(true);
                setStatusChangeData({
                  id: record?.id,
                  currentStatus: !record?.status,
                });
              }}
              checked={record?.status}
            />
          </div>
        );
      },
    },
    {
      title: <span className="w-full flex justify-center">Action</span>,
      key: "action",
      width: 120,
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center gap-2">
            <EditButton
              onClick={() => {
                setEditUserModalData({
                  id: record?.id,
                  avatar: record?.avatar,
                  fullName: record?.fullName,
                  email: record?.email,
                  countryCode: record?.countryCode,
                  mobileNumber: record?.mobileNumber,
                  emergencyNumber: record?.emergencyNumber,
                  emergencyNumberCountryCode:
                    record?.emergencyNumberCountryCode,
                  bloodGroup: record?.bloodGroup,
                  age: record?.age,
                });
                setOpenEditUserModal(true);
              }}
            />
            <ViewButton
              onClick={() => {
                navigate(browserRoutes?.viewUser, {
                  state: record,
                });
              }}
            />
            <DeleteButton
              onClick={() => {
                setOpenDeleteModal(true);
                setDeletedRecordId(record?.id);
              }}
            />
          </div>
        );
      },
      fixed: "right",
    },
  ];
  return registeredUserColumns;
};

import React, { useEffect, useState } from "react";
import { SearchOutlined, TeamOutlined } from "@ant-design/icons";
import { Tabs } from "antd";

import PageWrapper from "../../components/common/PageWrapper";
import CustomTable from "../../components/common/CustomTable";
import Api from "../../utils/apiHandler";
import { ApiRoute } from "../../config/apiEndpoint";
import { showToast } from "../../utils/showToast";
import { showError } from "../../utils/showError";
import CustomInput from "../../components/form/CustomInput";
import ViewModal from "./ViewModal";
import PageTitle from "../../components/common/PageTitle";
import { getColumns } from "./utils";
import StatusChangeConfirmModal from "./StatusChangeConfirmModal ";
import EditUserModal from "./EditUserModal";
import DeleteConfirmModal from "../../components/common/DeleteConfirmModal";
import { useNavigate } from "react-router-dom";
let timeoutVar;

const Users = () => {
  const [tableData, setTableData] = useState([]);

  const navigate = useNavigate();
  const [tableLoading, setTableLoading] = useState(true);
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10,
    },
    searchTerm: "",
  });
  const [searchInputValue, setSearchInputValue] = useState("");
  const [openStatusChangeModal, setOpenStatusChangeModal] = useState(false);
  const [statusLoading, setStatusLoading] = useState(false);
  const [statusChangeData, setStatusChangeData] = useState({
    id: null,
    currentStatus: null,
  });
  const [openEditUserModal, setOpenEditUserModal] = useState(false);
  const [editUserModalData, setEditUserModalData] = useState(null);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deletedRecordId, setDeletedRecordId] = useState(null);
  const getTableData = async (manageLoading = true) => {
    try {
      let payload = {
        page: Number(tableParams?.pagination?.current),
        sort_by: tableParams?.column?.key
          ? tableParams?.column?.key
          : "fullName",
        sort_order: tableParams?.order === "ascend" ? "asc" : "desc",
        search: tableParams?.searchTerm ?? "",
      };
      if (manageLoading) {
        setTableLoading(true);
      }
      const res = {
        data: {
          "status": 1,
          "message": "User list fetched successfully.",
          "data": {
            "count": 5,
            "rows": [
              {
                "avatar": null,
                "bannerImage": null,
                "qrCode": null,
                "id": 29,
                "fullName": "Hetvi Shah",
                "email": "<EMAIL>",
                "mobileNumber": "8849614730",
                "countryCode": "+91",
                "emergencyNumber": "8849614731",
                "emergencyNumberCountryCode": "+91",
                "bloodGroup": null,
                "dateOfBirth": "2007-05-29",
                "step": 2,
                "otp": null,
                "otpExpire": null,
                "isTerms": true,
                "password": "$2b$10$hAw68c5Rtq/sRdQzUakDmeakVGBrdx47qH90e6kr5DXRxrBdLXU3W",
                "resetPasswordOtp": null,
                "resetPasswordOtpExpire": null,
                "status": false,
                "isverified": true,
                "subscriptionPlan": "Premium Plan",
                "interestIds": null,
                "steps": null,
                "facebook": null,
                "instagram": null,
                "youtube": null,
                "snapchat": null,
                "website": null,
                "bio": null,
                "UpiId": null,
                "gender": "female",
                "uniqueId": "C317256K",
                "notification": true,
                "socketId": null,
                "createdAt": "2025-05-28T20:17:44.000Z",
                "updatedAt": "2025-06-06T09:32:09.000Z",
                "deletedAt": null
              },
              {
                "avatar": null,
                "bannerImage": null,
                "qrCode": null,
                "id": 28,
                "fullName": "John Doe",
                "email": "<EMAIL>",
                "mobileNumber": "9987654321",
                "countryCode": "+91",
                "emergencyNumber": "9087654321",
                "emergencyNumberCountryCode": "+91",
                "bloodGroup": null,
                "dateOfBirth": "2007-05-29",
                "step": 1,
                "otp": 2957,
                "otpExpire": "2025-05-28T20:18:31.000Z",
                "isTerms": true,
                "password": "$2b$10$5hYpiA.Oq0V.cBJxm2JgTuLa0JvWMrSaoPZfGrAJbqC3NAtdnZKMC",
                "resetPasswordOtp": null,
                "resetPasswordOtpExpire": null,
                "status": true,
                "isverified": false,
                "subscriptionPlan": "Basic Plan",
                "interestIds": null,
                "steps": null,
                "facebook": null,
                "instagram": null,
                "youtube": null,
                "snapchat": null,
                "website": null,
                "bio": null,
                "UpiId": null,
                "gender": "female",
                "uniqueId": "Y157314A",
                "notification": true,
                "socketId": null,
                "createdAt": "2025-05-28T20:16:01.000Z",
                "updatedAt": "2025-05-28T20:16:01.000Z",
                "deletedAt": null
              },
              {
                "avatar": null,
                "bannerImage": null,
                "qrCode": null,
                "id": 27,
                "fullName": "Sweta Shah",
                "email": "",
                "mobileNumber": "0987654321",
                "countryCode": "+91",
                "emergencyNumber": "9087654321",
                "emergencyNumberCountryCode": "+91",
                "bloodGroup": null,
                "dateOfBirth": "2007-05-29",
                "step": 1,
                "otp": 1495,
                "otpExpire": "2025-05-28T20:18:13.000Z",
                "isTerms": true,
                "password": "$2b$10$92dpubgmlhEIMKWzDXNGWeeU4bq7mK5.x0ncMPYHf0yo32svXY.QG",
                "resetPasswordOtp": null,
                "resetPasswordOtpExpire": null,
                "status": true,
                "isverified": false,
                "subscriptionPlan": null,
                "interestIds": null,
                "steps": null,
                "facebook": null,
                "instagram": null,
                "youtube": null,
                "snapchat": null,
                "website": null,
                "bio": null,
                "UpiId": null,
                "gender": "female",
                "uniqueId": "N339548Y",
                "notification": true,
                "socketId": null,
                "createdAt": "2025-05-28T20:15:42.000Z",
                "updatedAt": "2025-05-28T20:15:43.000Z",
                "deletedAt": null
              },
              {
                "avatar": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/dot-we/user/0gX6h-1750183632769.jpg",
                "bannerImage": null,
                "qrCode": null,
                "id": 31,
                "fullName": "Suzanna Williams",
                "email": "<EMAIL>",
                "mobileNumber": "9740368585",
                "countryCode": "+91",
                "emergencyNumber": "8884518837",
                "emergencyNumberCountryCode": "+91",
                "bloodGroup": "O+",
                "dateOfBirth": "1992-02-15",
                "step": 4,
                "otp": null,
                "otpExpire": null,
                "isTerms": true,
                "password": "$2b$10$QMot9gPrNhj37C7ETE2Veeb/hZsedYYod1LX5l2eJgRFC4vjja/CC",
                "resetPasswordOtp": null,
                "resetPasswordOtpExpire": null,
                "status": true,
                "isverified": true,
                "subscriptionPlan": "Family Plan",
                "interestIds": null,
                "steps": null,
                "facebook": null,
                "instagram": null,
                "youtube": null,
                "snapchat": null,
                "website": null,
                "bio": null,
                "UpiId": null,
                "gender": "female",
                "uniqueId": "T622893K",
                "notification": false,
                "socketId": null,
                "createdAt": "2025-06-17T18:07:16.000Z",
                "updatedAt": "2025-06-17T18:59:45.000Z",
                "deletedAt": "2025-06-17T18:59:45.000Z"
              },
              {
                "avatar": null,
                "bannerImage": null,
                "qrCode": null,
                "id": 32,
                "fullName": "Sara Susan Eapen",
                "email": "<EMAIL>",
                "mobileNumber": "8296082461",
                "countryCode": "+91",
                "emergencyNumber": "9916160290",
                "emergencyNumberCountryCode": "+91",
                "bloodGroup": "AB+",
                "dateOfBirth": "1995-01-01",
                "step": 4,
                "otp": null,
                "otpExpire": null,
                "isTerms": true,
                "password": "$2b$10$LAFVy.94zCKBwWPVhZuXWeEGaEQ/.NlM0NqcovukbiBlZNsxGMii2",
                "resetPasswordOtp": null,
                "resetPasswordOtpExpire": null,
                "status": true,
                "isverified": true,
                "subscriptionPlan": "Standard Plan",
                "interestIds": null,
                "steps": null,
                "facebook": null,
                "instagram": null,
                "youtube": null,
                "snapchat": null,
                "website": null,
                "bio": null,
                "UpiId": null,
                "gender": "female",
                "uniqueId": "Y444437V",
                "notification": true,
                "socketId": null,
                "createdAt": "2025-06-18T13:33:44.000Z",
                "updatedAt": "2025-06-18T13:35:34.000Z",
                "deletedAt": null
              }
            ]
          }
        }
      }
      //await Api("POST", ApiRoute?.userList, payload);
      if (res?.data?.status) {
        setTableData(res?.data?.data?.rows);
        setTableParams((prev) => {
          return {
            ...prev,
            pagination: {
              ...prev.pagination,
              total: res?.data?.data?.count,
            },
          };
        });
      } else {
        setTableData([]);
        setTableParams((prev) => {
          return {
            ...prev,
            pagination: {
              current: 1,
              pageSize: 10,
            },
          };
        });
        showToast.error(res?.data?.message);
      }
      if (manageLoading) {
        setTableLoading(false);
      }
    } catch (err) {
      if (manageLoading) {
        setTableLoading(false);
      }
      setTableData([]);
      setTableParams((prev) => {
        return {
          ...prev,
          pagination: {
            current: 1,
            pageSize: 10,
          },
        };
      });
      showError(err);
    }
  };

  // function to handle sorting and pagination
  const handleTableChange = (pagination, filters, sorter) => {
    setTableParams((prev) => {
      return {
        ...prev,
        pagination,
        ...filters,
        ...sorter,
      };
    });
    // `dataSource` is useless since `pageSize` changed
    if (pagination.pageSize !== tableParams.pagination?.pageSize) {
      setTableData([]);
    }
  };

  const handleSorterHeaderCellClick = (column) => {
    let sorter = {
      column: undefined,
      columnKey: column?.key,
      field: column?.dataIndex,
      order: undefined,
    };
    if (!column?.sortOrder) {
      sorter = {
        ...sorter,
        column: column,
        order: "ascend",
      };
    } else if (column?.sortOrder === "ascend") {
      sorter = {
        ...sorter,
        column: column,
        order: "descend",
      };
    } else if (column?.sortOrder === "descend") {
      sorter = {
        ...sorter,
        column: undefined,
        order: undefined,
      };
    }
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
      ...sorter,
    });
  };

  const handleSearchInputChange = (e) => {
    setSearchInputValue(e?.target?.value);
    if (timeoutVar) {
      clearTimeout(timeoutVar);
    }
    timeoutVar = setTimeout(() => {
      setTableParams({
        ...tableParams,
        pagination: {
          ...tableParams.pagination,
          current: 1,
        },
        searchTerm: e?.target?.value,
      });
    }, 500);
  };

  const handleStatusChange = async () => {
    // try {
    //   setStatusLoading(true);
    //   const res = await Api("POST", ApiRoute.userChangeStatus, {
    //     id: statusChangeData?.id,
    //     status: statusChangeData?.currentStatus,
    //   });

    //   if (res?.data?.status) {
    //     if (tableParams?.pagination?.current == 1) {
    //       await getTableData(false);
    //     } else {
    //       setTableParams({
    //         ...tableParams,
    //         pagination: {
    //           ...tableParams.pagination,
    //           current: 1,
    //         },
    //       });
    //     }
    //     setStatusChangeData({ currentStatus: null, id: null });
    setOpenStatusChangeModal(false);
    //     showToast.success(res?.data?.message);
    //   } else {
    //     showToast.error(res?.data?.message);
    //   }
    //   setStatusLoading(false);
    // } catch (err) {
    //   setStatusLoading(false);
    //   showError(err);
    // }
  };

  const handleDelete = async () => {
    // try {
    //   setDeleteLoading(true);
    //   const res = await Api(
    //     "DELETE",
    //     ApiRoute?.userDelete + `?id=${deletedRecordId}`
    //   );
    //   if (res?.data?.status) {
    //     if (tableData?.length > 1 || tableParams?.pagination?.current == 1) {
    //       await getTableData(false);
    //     } else {
    //       let currentPage = Number(tableParams?.pagination?.current) - 1;
    //       setTableParams({
    //         ...tableParams,
    //         pagination: {
    //           ...tableParams.pagination,
    //           current: currentPage,
    //         },
    //       });
    //     }
    //     setDeleteLoading(false);
    //     setDeletedRecordId(null);
    //     setOpenDeleteModal(false);
    //     showToast.success(res?.data?.message);
    //   } else {
    //     setDeleteLoading(false);
    //     showToast.error(res?.data?.message);
    //   }
    // } catch (err) {
    //   setDeleteLoading(false);
    //   showError(err);
    // }
  };
  // get table data
  useEffect(() => {
    getTableData();
  }, [
    tableParams?.pagination?.current,
    tableParams?.order,
    tableParams?.column?.key,
    JSON.stringify(tableParams?.filters),
    tableParams?.searchTerm,
  ]);

  return (
    <PageWrapper isScrollable={false}>
      <div className="h-full flex flex-col overflow-hidden">
        <div className="flex justify-between items-center gap-3">
          <PageTitle title="Users" icon={<TeamOutlined />} />

          <div className="flex items-center gap-3">
            <CustomInput
              placeholder={"Search by name"}
              prefix={<SearchOutlined className="text-[#d9d9d9]" />}
              value={searchInputValue}
              onChange={handleSearchInputChange}
              allowClear
            />
          </div>
        </div>
        <div className="flex-1 overflow-hidden pt-4">
          <CustomTable
            rowKey={(record) => record?.userId}
            scrollX={1200} //scrollX is sum of all unfixed column width
            columns={getColumns({
              tableParams,
              handleSorterHeaderCellClick,
              setOpenStatusChangeModal,
              setStatusChangeData,
              setOpenDeleteModal,
              setDeletedRecordId,
              setEditUserModalData,
              setOpenEditUserModal,
              navigate,
            })}
            dataSource={tableData}
            onChange={handleTableChange}
            pagination={tableParams?.pagination}
            loading={tableLoading}
          />
        </div>
      </div>
      {/* Status Change Modal */}
      {openStatusChangeModal && (
        <StatusChangeConfirmModal
          title={
            !statusChangeData?.currentStatus
              ? "Suspend User Account"
              : "Reactivate User Account"
          }
          message={
            !statusChangeData?.currentStatus
              ? "Are you sure you want to suspend this user account? Once suspended, the user will no longer be able to log in or access their account."
              : "Are you sure you want to reactivate this user account? Once reactivated, the user will regain access to their account and be able to log in."
          }
          open={openStatusChangeModal}
          onCancel={() => setOpenStatusChangeModal(false)}
          currentStatus={statusChangeData?.currentStatus}
          handleStatusChange={handleStatusChange}
          loading={statusLoading}
        />
      )}
      {openEditUserModal && (
        <EditUserModal
          open={openEditUserModal}
          onCancel={() => setOpenEditUserModal(false)}
          editUserModalData={editUserModalData}
          onSuccess={async () => {
            setEditUserModalData(null);
            await getTableData(false);
          }}
        />
      )}
      {openDeleteModal && (
        <DeleteConfirmModal
          title={"Delete User"}
          description={"Are you sure you want to delete this user?"}
          open={openDeleteModal}
          onCancel={() => setOpenDeleteModal(false)}
          handleDelete={handleDelete}
          deleteLoading={deleteLoading}
        />
      )}
    </PageWrapper>
  );
};

export default Users;

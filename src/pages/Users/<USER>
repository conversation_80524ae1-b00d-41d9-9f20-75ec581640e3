import { UserOutlined, CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Avatar, Tag, Button } from "antd";

import {
  DeleteButton,
  EditButton,
  ViewButton,
} from "../../components/common/TableButtons";
import { browserRoutes } from "../../config/browserRoutes";

export const getColumns = ({
  tableParams,
  handleSorterHeaderCellClick,
  setOpenStatusChangeModal,
  setStatusChangeData,
  setOpenDeleteModal,
  setDeletedRecordId,
  setEditUserModalData,
  setOpenEditUserModal,
  navigate,
}) => {
  const registeredUserColumns = [
    {
      title: <span className="w-full flex justify-center">Profile</span>,
      dataIndex: "avatar",
      key: "avatar",
      width: 88,
      render: (value, record) => {
        return (
          <div className="w-full flex justify-center">
            {value ? (
              <Avatar className="w-14 h-14" src={value} />
            ) : (
              <Avatar className="w-14 h-14" icon={<UserOutlined />} />
            )}
          </div>
        );
      },
    },
    {
      title: "Full Name",
      dataIndex: "fullName",
      key: "fullName",
      align: "center",
      width: 250,
      render: (value, record) => {
        return <div className="truncate">{value ? value : "-"}</div>;
      },
      sorter: () => true,
      sortOrder:
        tableParams?.columnKey === "fullName" ? tableParams?.order : null,
      onHeaderCell: (column) => {
        return {
          onClick: () => handleSorterHeaderCellClick(column),
        };
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      align: "center",
      width: 300,
      render: (value, record) => {
        return <div className="truncate">{value ? value : "-"}</div>;
      },
      sorter: () => true,
      sortOrder: tableParams?.columnKey === "email" ? tableParams?.order : null,
      onHeaderCell: (column) => {
        return {
          onClick: () => handleSorterHeaderCellClick(column),
        };
      },
    },
    {
      title: <span className="w-full flex justify-center">Email Verified</span>,
      dataIndex: "isverified",
      key: "isverified",
      align: "center",
      width: 120,
      render: (value, record) => {
        return (
          <div className="w-full flex justify-center">
            {value ? (
              <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
            ) : (
              <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
            )}
          </div>
        );
      },
    },
    {
      title: "Mobile number",
      dataIndex: "mobileNumber",
      key: "mobileNumber",
      align: "center",
      width: 200,
      render: (_, record) => {
        return (
          <div className="truncate">
            {record?.mobileNumber
              ? record?.countryCode + " " + record?.mobileNumber
              : "-"}
          </div>
        );
      },
    },
    {
      title: "Subscription Plan",
      dataIndex: "subscriptionPlan",
      key: "subscriptionPlan",
      align: "center",
      width: 150,
      render: (value, record) => {
        const planColors = {
          "Basic Plan": "blue",
          "Standard Plan": "green",
          "Premium Plan": "gold",
          "Family Plan": "purple"
        };
        return (
          <div className="w-full flex justify-center">
            {value ? (
              <Tag color={planColors[value] || "default"}>
                {value}
              </Tag>
            ) : (
              <Tag color="default">No Plan</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: <span className="w-full flex justify-center">Account Status</span>,
      key: "status",
      dataIndex: "status",
      width: 140,
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center">
            <Button
              size="small"
              type={record?.status ? "default" : "primary"}
              danger={record?.status}
              onClick={() => {
                setOpenStatusChangeModal(true);
                setStatusChangeData({
                  id: record?.id,
                  currentStatus: !record?.status,
                });
              }}
            >
              {record?.status ? "Suspend" : "Reactivate"}
            </Button>
          </div>
        );
      },
    },
    {
      title: <span className="w-full flex justify-center">Action</span>,
      key: "action",
      width: 120,
      render: (_, record) => {
        return (
          <div className="w-full flex justify-center gap-2">
            <EditButton
              onClick={() => {
                setEditUserModalData({
                  id: record?.id,
                  avatar: record?.avatar,
                  fullName: record?.fullName,
                  email: record?.email,
                  countryCode: record?.countryCode,
                  mobileNumber: record?.mobileNumber,
                  emergencyNumber: record?.emergencyNumber,
                  emergencyNumberCountryCode:
                    record?.emergencyNumberCountryCode,
                  bloodGroup: record?.bloodGroup,
                  age: record?.age,
                });
                setOpenEditUserModal(true);
              }}
            />
            <ViewButton
              onClick={() => {
                navigate(browserRoutes?.viewUser, {
                  state: record,
                });
              }}
            />
            <DeleteButton
              onClick={() => {
                setOpenDeleteModal(true);
                setDeletedRecordId(record?.id);
              }}
            />
          </div>
        );
      },
      fixed: "right",
    },
  ];
  return registeredUserColumns;
};

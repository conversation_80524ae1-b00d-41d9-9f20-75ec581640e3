import { createGlobalStyle } from "styled-components";
import { themeConfig } from "../../config/theme.config";

const GlobalStyle = createGlobalStyle` 
html {
  font-family: var(--font-family) !important;
  font-weight: 400 !important;
  font-style: normal !important;
}

* {
  box-sizing: border-box !important;
  padding: 0;
  margin: 0;
}

body {
  font-size: 14px !important;
  font-family: ${(props) =>
    props.fontFamily ? props.fontFamily : "sans-serif"} !important;

  // modal styles
  .ant-modal-root {
    .ant-modal-wrap {
      overflow-y: hidden !important;
      .ant-modal {
        .ant-modal-content {
          max-height: 90vh !important;
          display: flex !important;
          flex-direction: column !important;
          .ant-modal-body {
            flex: 1 !important;
            overflow-y: auto !important;
            // custom scrollbar 
            &::-webkit-scrollbar {
              width: 4px !important;
            }
            &::-webkit-scrollbar-thumb {
              background: rgba(1,1,1,0.2) !important; 
            }
          }
        }
      }
    }
  }
  .formSubmitLoaderModal{
    .ant-modal-content{
      padding-inline: 0 !important;
    }
  }
  .customTable{
    .ant-table-wrapper {
      overflow-wrap: auto !important;
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      white-space: nowrap !important; // Prevent text from wrapping
    }
    // Sticky column styling
    .ant-table-cell-fix-left {
      background: #fff !important;
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
      z-index: 1 !important;
    }
    .ant-table-thead .ant-table-cell-fix-left {
      background: #fafafa !important;
    }
    .ant-table-body{
      scrollbar-width: thin !important;
      // custom scrollbar 
      &::-webkit-scrollbar {
        width: 3px !important;
        height: 3px !important;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(1,1,1,0.1) !important; 
      }
      &::-webkit-scrollbar-thumb:hover {
        background: rgba(1,1,1,0.2) !important; 
      }
    }
      .ant-table-column-sorter-up {
        color: #fff !important;
      }
      .ant-table-column-sorter-up.active {
        color: ${themeConfig?.primaryColor} !important;
      }
      .ant-table-column-sorter-down {
        color: #fff !important;
      }
      .ant-table-column-sorter-down.active {
        color: ${themeConfig?.primaryColor} !important;
      }
  }
  .horizontalLine {
    border-top: 1px solid rgba(1, 1, 1, 0.1) !important;
  }
  // custom scrollbar
  .customScrollbarSm{
    &::-webkit-scrollbar {
      width: 3px !important;
    }
    &::-webkit-scrollbar-thumb {
      background: rgba(1,1,1,0.2) !important; 
    }
  }
  .customScrollbarMd{
    &::-webkit-scrollbar {
      width: 6px !important;
    }
    &::-webkit-scrollbar-thumb {
      background: rgba(1,1,1,0.2) !important; 
    }
  }
  // customize scrollbar in Select dropdown 
  .rc-virtual-list-scrollbar, .rc-virtual-list-scrollbar-vertical{
    width: 4px !important;
    .rc-virtual-list-scrollbar-thumb{
      background: rgba(1, 1, 1, 0.2) !important;
    }
  }
  // Hide up/down arrow from number input
  .hideInputUpDownarrow {
    /* Chrome, Safari, Edge, Opera */
    &::-webkit-outer-spin-button {
      -webkit-appearance: none !important;
      margin: 0 !important;
    }
    &::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0 !important;
    }
    /* Firefox */
    -moz-appearance: textfield !important;
  }
  .countryCodePopupRoot{
    .ant-popover-content{
      .ant-popover-inner{
        padding: 0 !important;
        border-radius: 6px !important;
      }
    }
    .countryItemsContainer{
      overflow-y: scroll !important;
      &::-webkit-scrollbar {
        width: 3px !important;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(1,1,1,0.2) !important; 
      }
    }
  }
  .ck.ck-balloon-panel.ck-powered-by-balloon .ck.ck-powered-by{
    display: none !important;
  }
}   
`;

export default GlobalStyle;

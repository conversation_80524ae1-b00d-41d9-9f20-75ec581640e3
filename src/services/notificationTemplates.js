// Shared notification templates service
// This would typically connect to an API, but for now we'll use mock data

export const getNotificationTemplates = async () => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        {
          id: 1,
          type: "trial_expiry_reminder",
          title: "Trial Expiry Reminder",
          subject: "Your PantryPal trial is expiring soon!",
          message: "Hi {userName}, your 7-day free trial of PantryPal Premium will expire on {expiryDate}. Upgrade now to continue enjoying all premium features!",
          createdAt: "2024-06-25",
          isActive: true,
        },
        {
          id: 2,
          type: "subscription_expiry_reminder",
          title: "Subscription Expiry Reminder",
          subject: "Your PantryPal subscription is expiring soon!",
          message: "Hi {userName}, your PantryPal Premium subscription will expire on {expiryDate}. Renew now to avoid interruption of service!",
          createdAt: "2024-06-24",
          isActive: true,
        },
        {
          id: 3,
          type: "welcome_trial",
          title: "Welcome to Trial",
          subject: "Welcome to PantryPal Premium Trial!",
          message: "Hi {userName}, welcome to your 7-day free trial of PantryPal Premium! Explore all the amazing features and see how we can transform your pantry management.",
          createdAt: "2024-06-23",
          isActive: true,
        },
        {
          id: 4,
          type: "upgrade_promotion",
          title: "Upgrade Promotion",
          subject: "Special offer: Upgrade to PantryPal Premium!",
          message: "Hi {userName}, we have a special offer for you! Upgrade to PantryPal Premium and get your first month at 50% off. Limited time offer!",
          createdAt: "2024-06-22",
          isActive: false,
        }
      ]);
    }, 500);
  });
};

export const getActiveNotificationTemplates = async () => {
  const templates = await getNotificationTemplates();
  return templates.filter(template => template.isActive);
};

export const createNotificationTemplate = async (templateData) => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id: Date.now(),
        ...templateData,
        createdAt: new Date().toISOString().split('T')[0],
      });
    }, 1000);
  });
};

export const updateNotificationTemplate = async (id, templateData) => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id,
        ...templateData,
        updatedAt: new Date().toISOString().split('T')[0],
      });
    }, 1000);
  });
};

export const deleteNotificationTemplate = async (id) => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 1000);
  });
};

// Template types for dropdowns
export const templateTypes = [
  { label: "Trial Expiry Reminder", value: "trial_expiry_reminder" },
  { label: "Subscription Expiry Reminder", value: "subscription_expiry_reminder" },
  { label: "Welcome to Trial", value: "welcome_trial" }
];

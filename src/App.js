import { lazy, Suspense, useEffect } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { ConfigProvider, message, Spin } from "antd";
import {
  BrowserRouter,
  Navigate,
  Outlet,
  Route,
  Routes,
} from "react-router-dom";
import ErrorBoundary from "./components/common/ErrorBoundary";
import store, { persistor } from "./store";
import PageNotFound from "./pages/PageNotFound";
import { themeConfig } from "./config/theme.config";
import { browserRoutes } from "./config/browserRoutes";
import { routerConfig } from "./config/router.config";
import GlobalStyle from "./assets/styles/globals.style";
import "./assets/styles/index.css";
import { setMessageInstance } from "./utils/showToast";

const IndexLayout = lazy(() => import("./layouts/IndexLayout"));
const DashboardLayout = lazy(() => import("./layouts/DashboardLayout"));

function App() {
  // global variables for css
  useEffect(() => {
    document.documentElement.style.setProperty(
      "--font-family",
      themeConfig?.fontFamily
    );
  }, [themeConfig?.fontFamily]);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    setMessageInstance(messageApi);
  }, [messageApi]);
  return (
    <>
      {contextHolder}
      <GlobalStyle fontFamily={themeConfig?.fontFamily} />
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <ConfigProvider
            theme={{
              token: {
                colorPrimary: themeConfig?.primaryColor,
                fontFamily: themeConfig?.fontFamily,
                colorError: themeConfig?.errorColor,
                colorPrimaryBg: themeConfig?.primaryColorLight,
              },
              components: {
                Layout: {
                  motionDurationMid: "0.7s",
                },
                Menu: {
                  itemSelectedBg: themeConfig?.primaryColor,
                  itemSelectedColor: "#fff",
                  itemBorderRadius: 6,
                },
                Table: {
                  headerBg: themeConfig?.primaryColor,
                  headerSplitColor: themeConfig?.secondaryColor,
                  headerSortHoverBg: themeConfig?.primaryColorLight,
                  headerSortActiveBg: themeConfig?.primaryColorLight,
                  headerColor: themeConfig?.secondaryColor,
                  filterDropdownBg: themeConfig?.secondaryColor,
                },
                Modal: {
                  zIndexPopupBase: 1051,
                },
              },
            }}
          >
            <ErrorBoundary>
              <BrowserRouter>
                <Suspense
                  fallback={
                    <div className="w-screen h-screen flex justify-center items-center">
                      <Spin spinning={true} />
                    </div>
                  }
                >
                  <Routes>
                    <Route
                      path="/"
                      exact
                      element={<Navigate to={browserRoutes?.analytics} />}
                    />
                    {/* index routes */}
                    <Route
                      element={
                        <IndexLayout>
                          <Outlet />
                        </IndexLayout>
                      }
                    >
                      {routerConfig?.indexRoutes?.length > 0 &&
                        routerConfig?.indexRoutes?.map((item, i) => {
                          return (
                            <Route
                              path={item?.path}
                              element={item?.component}
                              key={i}
                            />
                          );
                        })}
                    </Route>
                    {/* dashboard routes */}
                    <Route
                      element={
                        <DashboardLayout>
                          <Outlet />
                        </DashboardLayout>
                      }
                    >
                      {routerConfig?.dashboardRoutes?.length > 0 &&
                        routerConfig?.dashboardRoutes?.map((item, i) => {
                          return (
                            <Route
                              path={item?.path}
                              element={item?.component}
                              key={i}
                            />
                          );
                        })}
                    </Route>
                    <Route path="/404" element={<PageNotFound />} />
                    <Route path="*" element={<Navigate to={"/404"} />} />
                  </Routes>
                </Suspense>
              </BrowserRouter>
            </ErrorBoundary>
          </ConfigProvider>
        </PersistGate>
      </Provider>
    </>
  );
}

export default App;

import { Table } from "antd";
import React, { useEffect, useRef, useState } from "react";

const CustomTable = ({
  rowKey,
  columns,
  dataSource,
  onChange,
  loading,
  pagination,
  scrollX,
  rowSelection,
  props,
}) => {
  const [tableHeight, setTableHeight] = useState(window.innerHeight - 200); // Increase initial height
  const containerRef = useRef(null);

  useEffect(() => {
    // Set initial height
    setTableHeight(window.innerHeight - 200);

    // Add window resize listener
    const handleResize = () => {
      if (containerRef?.current) {
        setTableHeight(containerRef.current.clientHeight - 100);
      } else {
        setTableHeight(window.innerHeight - 200);
      }
    };

    window.addEventListener('resize', handleResize);

    // Initial calculation after component mounts
    setTimeout(() => {
      if (containerRef?.current) {
        setTableHeight(containerRef.current.clientHeight - 100);
      }
    }, 100);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="h-full customTable" ref={containerRef} style={{ minHeight: '500px' }}>
      <Table
        size="middle"
        rowKey={rowKey}
        columns={columns}
        dataSource={dataSource}
        onChange={onChange}
        loading={loading}
        pagination={
          pagination
            ? {
              ...pagination,
              position: ["none", "bottomCenter"],
              size: "default",
              showSizeChanger: false,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} items`,
            }
            : false
        }
        scroll={{
          x: scrollX,
          y: tableHeight > 100 ? tableHeight : 400, // Ensure minimum height
        }}
        rowSelection={rowSelection}
        {...props}
      />
    </div>
  );
};

export default CustomTable;

import * as Yup from "yup";

const SUPPORTED_FORMATS = ["image/jpg", "image/jpeg", "image/png"];
const FILE_SIZE = 1 * 1024 * 1024;
const MIN_PASS_LEN = 5;
const MAX_PASS_LEN = 15;
const passwordRegex = new RegExp(
  `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\\d!@#$%^&*(),.?":{}|<>]{${MIN_PASS_LEN},}$`
);

// common fields validation shape
const imageShape = Yup.mixed()
  .nullable()
  // .required("Please upload your image.")
  .test("FILE_FORMAT", "Image must be in jpg, jpeg or png format.", (value) => {
    if (typeof value === "string" && value !== "") {
      return true;
    }
    return !value || (value && SUPPORTED_FORMATS.includes(value.type));
  })
  .test("FILE_SIZE", "Image size must be less than 1Mb.", (value) => {
    if (typeof value === "string" && value !== "") {
      return true;
    }
    return !value || (value && value.size <= FILE_SIZE);
  });
const emailShape = Yup.string()
  .email("Please enter valid email.")
  .required("Please enter email.");
const firstNameShape = Yup.string()
  .matches(/^[a-zA-Z]{1,25}$/, "Please enter valid first name.")
  .required("Please enter first name.");
const lastNameShape = Yup.string()
  .matches(/^[a-zA-Z]{1,25}$/, "Please enter valid last name.")
  .required("Please enter last name.");
const phoneNoShape = Yup.string()
  .matches(/^[5-9]\d{9}$/, "Please enter valid mobile no.")
  .required("Please enter mobile no.");

// form validation schemas
export const loginSchema = Yup.object({
  email: emailShape,
  password: Yup.string().required("Please enter password."),
});

export const createAccountSchema = Yup.object({
  firstName: firstNameShape,
  lastName: lastNameShape,
  email: emailShape,
  password: Yup.string()
    .required("Please enter password.")
    .matches(
      passwordRegex,
      `Password must be atleast ${MIN_PASS_LEN} characters with atleast one of each: uppercase, lowercase and number.`
    ),
  confirmPassword: Yup.string()
    .required("Please re-enter password.")
    .oneOf([Yup.ref("password"), null], "Both passwords must match."),
});

export const forgotPasswordSchema = Yup.object({
  email: emailShape,
});

export const resetPasswordSchema = Yup.object({
  newPassword: Yup.string()
    .required("Please enter new password.")
    .matches(
      passwordRegex,
      `Password must be atleast ${MIN_PASS_LEN} characters with atleast one of each: uppercase, lowercase and number.`
    ),
  confirmPassword: Yup.string()
    .required("Please re-enter new password.")
    .oneOf([Yup.ref("newPassword"), null], "Both passwords must match."),
});

export const changePasswordSchema = Yup.object({
  oldPassword: Yup.string().required("Please enter current password."),
  newPassword: Yup.string()
    .required("Please enter new password.")
    .matches(
      passwordRegex,
      `Password must be atleast ${MIN_PASS_LEN} characters with atleast one of each: uppercase, lowercase and number.`
    ),
  confirmPassword: Yup.string()
    .required("Please re-enter new password.")
    .oneOf([Yup.ref("newPassword"), null], "Both passwords must match."),
});

export const userSchema = Yup.object({
  fullName: Yup.string().required("Please enter full name."),
  email: Yup.string().email("Please enter valid email."),
  mobileNumber: Yup.string()
    .matches(/^\d+$/, "Please enter valid phone number.")
    .min(10, "Phone number must be at least 10 digits.")
    .max(15, "Phone number must not exceed 15 digits."),
});
export const interestSchema = Yup.object({
  description: Yup.string().required("Please enter Interest Description ."),
});
export const questionSchema = Yup.object({
  questionTitle: Yup.string().required("Please enter page title."),
  questionName: Yup.string().required("Please enter question name."),
  optionList: Yup.array().of(
    Yup.object({
      optionName: Yup.string().required("Please enter option name."),
    })
  ),
});

export const eventSchema = Yup.object().shape({
  bannerImage: Yup.mixed().required("Please upload event banner image."),
  name: Yup.string().required("Please enter event name."),
  startDate: Yup.string().required("Please enter event start date."),
  endDate: Yup.string().required("Please enter event end date."),
  duration: Yup.string().required("Please enter event duration."),
  ageLimit: Yup.string().required("Please enter age limit."),
  eventTags: Yup.array()
    .min(1, "Please select atleast 1 Event Tag.")
    .required("Please select Event Tag."),
  languageIds: Yup.string().required("Please select language."),
  address: Yup.object()
    .test("address-validation", "Please select a valid address", (value) => {
      // This custom test will return true if valid, false if invalid
      return value?.address && value?.lat && value?.lng;
    })
    .required("Please select event address"),
  eventHighlights: Yup.array()
    .min(1, "Please select atleast 1 Event Highlight.")
    .required("Please select Event Highlight."),
  ticketPrice: Yup.number().required("Please enter ticket price."),
  bio: Yup.string().optional(),
  tAndC: Yup.string().optional(),
  cancellationPolicy: Yup.string().optional(),
});

export const staticPageSchema = Yup.object({
  content: Yup.string().required("Please enter content."),
  title: Yup.string().required("Please enter title."),
  type: Yup.string()
    .matches(/^[a-zA-Z_]{1,25}$/, "Please enter valid first name.")
    .required("Please Enter type."),
  //  Yup.string().required("Please select type."),
});

export const addBrandSchema = Yup.object({
  brandName: Yup.string().required("Please Enter the Name of Brand"),
});
export const addBrandModelSchema = Yup.object({
  name: Yup.string().required("Please Enter the Name of Model"),
});
export const addTagSchema = Yup.object({
  name: Yup.string().required("Please Enter the Name of Tag"),
});
export const addHighlightSchema = Yup.object({
  image: Yup.mixed().required("Please upload highlight image."),
  name: Yup.string().required("Please Enter the Name of Highlight"),
});
export const addFaqSchema = Yup.object({
  question: Yup.string().required("Please Enter the Question of FAQ"),
  answer: Yup.string().required("Please Enter the Answer of Question"),
});
export const addCategorySchema = Yup.object({
  image: Yup.mixed().required("Please upload Category image."),
  name: Yup.string().required("Please Enter the Name of Category"),
});

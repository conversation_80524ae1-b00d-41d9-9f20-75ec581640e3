{"name": "bike-rider-community-app", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.6.1", "@ckeditor/ckeditor5-react": "^9.5.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@reduxjs/toolkit": "^2.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.24.3", "axios": "^1.8.1", "ckeditor5": "^44.3.0", "dompurify": "^3.2.6", "formik": "^2.4.6", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "recharts": "^3.0.2", "redux-persist": "^6.0.0", "redux-persist-indexeddb-storage": "^1.0.4", "styled-components": "^6.1.15", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}